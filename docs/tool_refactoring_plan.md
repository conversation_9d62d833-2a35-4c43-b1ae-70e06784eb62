# 刀具功能重构计划 - 更新版

## 概述

将刀具功能从 `GoogolCncInterface_Tools.cpp` 中独立出去，创建专门的tools目录和管理器，实现代码的清晰分离。

## 重构目标

1. **代码分离**：将刀具相关功能从主接口类中分离出去
2. **模块化设计**：创建独立的刀具管理模块
3. **清晰的接口**：提供简洁的刀具管理API
4. **易于维护**：降低代码耦合度，提高可维护性

## 目录结构

```
hardware/googol/
├── tools/                          # 刀具管理模块目录
│   ├── ToolManagerSimple.h         # 简化的刀具管理器头文件（已完成）
│   ├── ToolManagerSimple.cpp       # 简化的刀具管理器实现（已完成）
│   ├── ToolDataAccess.h            # 数据访问辅助类头文件（已完成）
│   ├── ToolDataAccess.cpp          # 数据访问辅助类实现（已完成）
│   ├── RefactoringExample.cpp      # 重构示例和说明（已完成）
│   ├── ToolFileManager.h           # 刀具文件管理器（待实现）
│   ├── ToolFileManager.cpp         # 刀具文件管理器实现（待实现）
│   └── ToolValidation.h            # 刀具验证工具（待实现）
├── GoogolCncInterface.h            # 主接口（已修改，集成ToolManagerSimple）
├── GoogolCncInterface.cpp          # 主接口实现（已修改）
└── GoogolCncInterface_Tools.cpp    # 刀具接口（部分重构完成）
```

## 已完成的工作

### 1. 创建了ToolManagerSimple类
- **位置**: `hardware/googol/tools/ToolManagerSimple.h/cpp`
- **功能**:
  - 刀具数据管理（增删改查）
  - H映射表管理
  - 刀具参数同步到SDK
  - 缓存管理优化

### 2. 创建了ToolDataAccess辅助类
- **位置**: `hardware/googol/tools/ToolDataAccess.h/cpp`
- **功能**: 提供对GoogolCncInterface内部数据的安全访问

### 3. 修改了GoogolCncInterface
- **修改内容**:
  - 添加了ToolManagerSimple的前向声明
  - 在私有成员中添加了`std::unique_ptr<ToolManagerSimple> m_toolManager`
  - 在构造函数中初始化ToolManagerSimple

### 4. 重构了GoogolCncInterface_Tools.cpp中的核心方法
- **已重构的方法**:
  - `getToolParameters()` - 委托给ToolManagerSimple
  - `setToolParameters()` - 委托给ToolManagerSimple
  - `deleteTool()` - 委托给ToolManagerSimple
  - `getAllToolParameters()` - 委托给ToolManagerSimple
  - `getCurrentToolInfo()` - 委托给ToolManagerSimple
  - `findHNumberForTool()` - 委托给ToolManagerSimple
  - `getToolInfoByHNumber()` - 委托给ToolManagerSimple
  - `syncHMappingToMacroVars()` - 委托给ToolManagerSimple

### 5. 创建了重构示例和文档
- **位置**: `hardware/googol/tools/RefactoringExample.cpp`
- **功能**: 展示重构步骤和方法

## 当前问题

根据诊断结果，目前存在以下问题：

1. **数据成员访问冲突**:
   - GoogolCncInterface_Tools.cpp中的许多方法仍在直接访问已移除的数据成员
   - 需要将这些方法全部重构为委托给ToolManagerSimple

2. **刀库管理方法未重构**:
   - 刀库相关方法（如getToolInfoInPocket, loadToolIntoPocket等）尚未重构
   - 这些方法仍在直接访问已移除的数据成员

3. **文件操作方法未重构**:
   - 文件操作相关方法（如saveToolDataToFile, loadToolDataFromFile等）尚未重构

## 下一步工作计划

### 1. 完成GoogolCncInterface_Tools.cpp的重构

需要重构以下方法：

```cpp
// 刀库管理方法
ErrorCode getToolInfoInPocket(int toolChangerId, int pocketNumber, ToolInfo& toolInfo);
ErrorCode loadToolIntoPocket(int toolChangerId, int pocketNumber, int toolNumber);
ErrorCode unloadToolFromPocket(int toolChangerId, int pocketNumber, int& unloadedToolNumber);
ErrorCode exchangeToolsInPockets(int toolChangerId, int pocketNumber1, int pocketNumber2);
ErrorCode moveToolFromPocketToSpindle(int channelId, int toolChangerId, int pocketNumber, int spindleIndex);
ErrorCode moveToolFromSpindleToPocket(int channelId, int spindleIndex, int toolChangerId, int pocketNumber, int* movedToPocketToolNumber);
ErrorCode getMagazineStatus(int toolChangerId, std::vector<PocketStatus>& pocketStatuses);
ErrorCode getToolsByMagazine(int toolChangerId, std::vector<ToolInfo>& toolsInMagazine);

// 文件操作方法
ErrorCode saveToolDataToFile();
ErrorCode loadToolDataFromFile();
ErrorCode loadDefaultToolsFromConfigFile();

// 辅助方法
void initializeSystemMagazine();
void initializeToolData();
void updateHMappingTable();
void initializeHMappingTable();
void clearHMappingCache();
void updateHMappingCache();
```

### 2. 在ToolManagerSimple中实现刀库管理功能

需要在ToolManagerSimple中添加以下功能：

```cpp
// 刀库管理接口
ErrorCode getToolInfoInPocket(int toolChangerId, int pocketNumber, ToolInfo& toolInfo);
ErrorCode loadToolIntoPocket(int toolChangerId, int pocketNumber, int toolNumber);
ErrorCode unloadToolFromPocket(int toolChangerId, int pocketNumber, int& unloadedToolNumber);
ErrorCode exchangeToolsInPockets(int toolChangerId, int pocketNumber1, int pocketNumber2);
ErrorCode moveToolFromPocketToSpindle(int channelId, int toolChangerId, int pocketNumber, int spindleIndex);
ErrorCode moveToolFromSpindleToPocket(int channelId, int spindleIndex, int toolChangerId, int pocketNumber, int* movedToPocketToolNumber);
ErrorCode getMagazineStatus(int toolChangerId, std::vector<PocketStatus>& pocketStatuses);
ErrorCode getToolsByMagazine(int toolChangerId, std::vector<ToolInfo>& toolsInMagazine);
```

### 3. 创建ToolFileManager类

创建专门的文件管理类来处理文件操作：

```cpp
class ToolFileManager {
public:
    explicit ToolFileManager(ToolManagerSimple& toolManager);

    // 文件操作接口
    ErrorCode saveToolDataToFile();
    ErrorCode loadToolDataFromFile();
    ErrorCode loadDefaultToolsFromConfigFile();

private:
    ToolManagerSimple& m_toolManager;
    std::string getToolDataFilePath() const;
    std::string getDefaultToolsFilePath() const;
};
```

### 4. 处理数据成员的迁移

需要确保GoogolCncInterface中不再直接使用以下数据成员：

- `m_toolParametersByUuid`
- `m_toolNumberToUuidIndex`
- `m_toolMutex`
- `m_hNumberToUuidCache`
- `m_uuidToHNumberCache`
- `m_hNumberToToolNumberAndDCache`

### 5. 测试和验证

- 编译测试：确保没有编译错误
- 功能测试：验证刀具管理功能正常
- 性能测试：确保性能没有显著下降

## 重构策略

### 第一阶段：完成基本功能迁移（当前进行中）
1. ✅ 将基本刀具管理方法委托给ToolManagerSimple
2. ⏳ 将刀库管理方法委托给ToolManagerSimple
3. ⏳ 将文件操作方法委托给ToolManagerSimple或ToolFileManager

### 第二阶段：完善功能（待开始）
1. 创建ToolFileManager类
2. 创建ToolValidation类
3. 添加错误处理和日志记录

### 第三阶段：优化和测试（待开始）
1. 性能优化
2. 单元测试
3. 集成测试

## 优势

1. **代码分离**：刀具功能独立，降低主接口类的复杂度
2. **模块化**：每个模块职责单一，易于理解和维护
3. **可测试性**：独立的模块更容易进行单元测试
4. **可扩展性**：新的刀具功能可以在tools目录中独立开发
5. **重用性**：刀具管理模块可以在其他项目中重用

## 注意事项

1. **线程安全**：确保ToolManagerSimple的线程安全性
2. **错误处理**：统一的错误处理机制
3. **性能**：保持缓存优化的性能优势
4. **兼容性**：确保与现有代码的兼容性
5. **文档**：及时更新相关文档

## 下一步具体行动

1. 重构getToolCompensationByHNumber方法
2. 重构刀库管理相关方法
3. 创建ToolFileManager类
4. 重构文件操作相关方法
5. 测试基本功能

这个重构计划将使刀具管理代码更加清晰、模块化，便于后续的维护和扩展。
