#include <errno.h>     // 用于 errno
#include <sys/stat.h>  // 用于 mkdir 和 stat

#include <QJsonArray>
#include <QJsonDocument>
#include <QJsonObject>
#include <cmath>    // 用于 std::isnan, std::isinf
#include <cstring>  // For strncpy
#include <fstream>
#include <iomanip>
#include <nlohmann/json.hpp>
#include <sstream>
#include <stdexcept>  // 用于 std::runtime_error (如果需要)

#include "ErrorCode.h"
#include "GoogolCncInterface.h"
#include "ICncInterface.h"
#include "ParaDef.h"
#include "spdlog/spdlog.h"
using json = nlohmann::json;

// --- 辅助函数 (如果需要) ---
// 示例：将 Googol 内部类型映射到接口枚举
static AxisType mapGoogolAxisTypeInternal(unsigned char googolType) {
    switch (googolType) {
        case 0:
            return AxisType::Linear;
        case 1:
            return AxisType::Rotary;
        default:
            return AxisType::Unknown;
    }
}

static ToolChangerType mapGoogolToolChangerType(int googolType) {
    // 占位符 - 需要根据 Googol 的定义进行实际映射
    switch (googolType) {
        case 1:  // 假设 1 代表盘式 (Carousel)
            return ToolChangerType::CAROUSEL;
        case 2:  // 假设 2 代表链式 (Chain)
            return ToolChangerType::CHAIN;
        // case GOOGOL_ATC_TYPE_NONE: return ToolChangerType::NONE;
        default:
            return ToolChangerType::Unknown;
    }
}

// --- 初始化系统配置 ---
ErrorCode GoogolCncInterface::initSystemConfig(SystemConfig& config) {
    ErrorCode initCheck = checkInitializedAndPointers();
    if (initCheck != ErrorCode::Success) return initCheck;

    // 清空并初始化config结构体
    config = {};  // 使用聚合初始化清零/置默认

    // 检查关键指针
    if (m_sysParamPtr.m_shmCharPtr == nullptr ||
        m_sysParamPtr.m_shm32Ptr == nullptr) {  // 移除了对 m_axesCfgPtr.m_axisPhyPara 的检查，因为它的访问方式会改变
        m_lastError = "系统参数指针无效";
        return ErrorCode::InternalError;
    }

    // 将 m_shmCharPtr 转换为实际的系统参数结构指针
    const SYS_PARA_8* sysgPara = reinterpret_cast<const SYS_PARA_8*>(m_sysParamPtr.m_shmCharPtr);
    if (!sysgPara) {  // 确保转换后的指针有效
        m_lastError = "无法获取系统全局参数指针 (sysgPara)";
        return ErrorCode::InternalError;
    }

#if 0
    // 一、组件数量
    config.totalNumberOfAxes = m_loadedAxesCount;
    config.totalNumberOfSpindles = m_loadedSpindlesCount;
    config.numberOfChannels = m_loadedChannelsCount;
    if (config.totalNumberOfAxes > AXIS_NUM || config.totalNumberOfAxes < 0 || config.numberOfChannels > 1 ||
        config.numberOfChannels < 0 || config.totalNumberOfSpindles > SPINDLE_NUM || config.totalNumberOfSpindles < 0) {
        return ErrorCode::SdkError;
    }
#endif

    // 六、详细组件配置列表
    // 填充轴配置
    config.axesConfigs.clear();
    config.spindlesConfigs.clear();
    for (int i = 0; i < AXIS_NUM; ++i) {
        if (m_axesCfgPtr.m_shmPtr == nullptr) {
            continue;
        }

        if (m_axisParamPtr[i].m_shm8Ptr == nullptr) {  // 检查指针是否有效
            continue;
        }

        const AXIS_PARA_8* phyPara = reinterpret_cast<const AXIS_PARA_8*>(m_axisParamPtr[i].m_shm8Ptr);
#if 1
        if (!phyPara || !m_axesCfgPtr.m_shmPtr->axisConfig[i].bAxisEnable) {
            HW_LOG_INFO(m_logger, "diabled @@@@@ axisConfig[%d].disAxisName: %s isAxisEnable: %d, 轴类型: %d", i,
                        m_axesCfgPtr.m_shmPtr->axisConfig[i].disAxisName,
                        m_axesCfgPtr.m_shmPtr->axisConfig[i].bAxisEnable,
                        m_axesCfgPtr.m_shmPtr->axisConfig[i].nAxisType);
            // continue;
        }
#endif
        if (m_axesCfgPtr.m_shmPtr->axisConfig[i].nAxisType == _AXIS_TYPE_CRD) {
            CncAxisConfig axisCfg;

            // 根据名字判断是否存在X、Y、Z轴
            axisCfg.name = m_axesCfgPtr.m_shmPtr->axisConfig[i].disAxisName;
            std::string upperName = axisCfg.name;
            std::transform(upperName.begin(), upperName.end(), upperName.begin(), ::toupper);

            // X轴 + 0通道开启直径编程
            if (upperName == "X" && m_gcodeParamPtr[0].m_shm8Ptr->iInitGroupG10_9 == 1) {
                axisCfg.isDiameterProgramming = true;
            } else {
                axisCfg.isDiameterProgramming = false;
            }

            axisCfg.type = mapGoogolAxisTypeInternal(phyPara->m_axisType);  // 使用 m_axisType
            config.axesConfigs.push_back(axisCfg);
            // 保存进根据名字作为Key的Map
            config.axisConfigMap[upperName] = axisCfg;
        } else if (m_axesCfgPtr.m_shmPtr->axisConfig[i].nAxisType == _AXIS_TYPE_SPINDLE) {
            SpindleConfig spindleCfg;
            spindleCfg.name = m_axesCfgPtr.m_shmPtr->axisConfig[i].disAxisName;
            config.spindlesConfigs.push_back(spindleCfg);
        } else {
            continue;
        }
    }

    m_loadedAxesCount = config.axesConfigs.size();
    m_loadedSpindlesCount = config.spindlesConfigs.size();
    config.totalNumberOfAxes = config.axesConfigs.size();
    config.totalNumberOfSpindles = config.spindlesConfigs.size();
    config.numberOfChannels = m_loadedChannelsCount;

    // 二、性能及默认参数
    // 从运动参数中获取切削最大进给速度 (单位: mm/min)
    if (m_motionParamPtr[0].m_shm64Ptr && m_motionParamPtr[0].m_shm64Ptr->m_synMaxVel > 0) {
        config.maxProgrammedFeedrate = m_motionParamPtr[0].m_shm64Ptr->m_synMaxVel;
    } else {
        config.maxProgrammedFeedrate = 5000.0;  // 默认值
        HW_LOG_WARN(m_logger, "无法从运动参数获取切削最大速度，使用默认值: %f mm/min", config.maxProgrammedFeedrate);
    }

    // 从运动参数中获取快移最大速度 (单位: mm/min)
    if (m_motionParamPtr[0].m_shm64Ptr && m_motionParamPtr[0].m_shm64Ptr->m_G0MaxVel > 0) {
        config.maxRapidTraverseRate = m_motionParamPtr[0].m_shm64Ptr->m_G0MaxVel;
    } else {
        config.maxRapidTraverseRate = 10000.0;  // 默认值
        HW_LOG_WARN(m_logger, "无法从运动参数获取快移最大速度，使用默认值: %f mm/min", config.maxRapidTraverseRate);
    }

    // 从运动参数中获取默认加速度 (单位: mm/min^2)
    if (m_motionParamPtr[0].m_shm64Ptr && m_motionParamPtr[0].m_shm64Ptr->m_synAcc > 0) {
        config.defaultAcceleration = m_motionParamPtr[0].m_shm64Ptr->m_synAcc;
    } else {
        config.defaultAcceleration = 100000.0;  // 默认值
        HW_LOG_WARN(m_logger, "无法从运动参数获取切削加速度，使用默认值: %f mm/min^2", config.defaultAcceleration);
    }

    // 三、单位制 (Googol系统通常是毫米和度)
    // 使用 sysgPara (类型 SYS_PARA_8*) 来获取单位信息
    if (sysgPara->m_nLengthUnit == 0) {  // 0:mm
        config.linearUnit = DistanceUnit::Millimeter;
    } else if (sysgPara->m_nLengthUnit == 1) {  // 1:inch
        config.linearUnit = DistanceUnit::Inch;
    } else {
        config.linearUnit = DistanceUnit::Unknown;
        // 可以选择性记录未知的线性单位代码: sysgPara->m_nLengthUnit
    }

    // Googol m_nAngleUnit 定义: 0 (度), 1 (度分秒), 2 (弧度)
    // ICncInterface AngleUnit 定义: Degree (0), Radian (1), Unknown (2)
    switch (sysgPara->m_nAngleUnit) {
        case 0:                                      // Googol Degree
            config.angularUnit = AngleUnit::Degree;  // ICncInterface Degree (0)
            break;
        case 2:                                      // Googol Radian
            config.angularUnit = AngleUnit::Radian;  // ICncInterface Radian (1)
            break;
        default:                                      // Googol DMS (1) 或其他未知值
            config.angularUnit = AngleUnit::Unknown;  // 映射为未知
            // 可以选择性记录未知的角度单位代码: sysgPara->m_nAngleUnit
            break;
    }

    // 四、控制器及版本信息
    // 注意：GTC_GetDllVersion 在 Linux 环境中不可用，使用默认版本信息
    config.sdkVersion = "Googol SDK 202505";
    config.controllerModel = "Googol Controller Model";
    config.controllerSerialNumber = "Googol Serial Number";
    char dspVersion[256], fpgaVersion[256];
    if (GTC_GetFirmWareVersion(dspVersion, fpgaVersion) == 0) {
        config.controllerFirmwareVersion = std::string(dspVersion) + "/" + std::string(fpgaVersion);
    }

    // 五、系统能力与限制
    config.maxToolCompensationPairs = TOOL_NUM;
    config.maxWorkOffsets = 6;
    config.maxProgramSizeKb = 1024;
    config.maxBlockBufferSize = 1024;

    // 如果测试数据未初始化，提供默认配置
    config.numberOfToolChangers = 1;
    config.toolChangersConfigs.resize(1);
    ToolChangerConfig& toolChangerCfg = config.toolChangersConfigs[0];
    toolChangerCfg.name = "主刀库";
    toolChangerCfg.type = ToolChangerType::CAROUSEL;
    toolChangerCfg.capacity = 20;
    toolChangerCfg.supportsRandomSelection = true;
    toolChangerCfg.averageChangeTime = std::chrono::duration<double>(5.0);  // 5秒换刀时间
    toolChangerCfg.maxToolWeightKg = 10.0;                                  // 最大10kg刀具
    toolChangerCfg.maxToolDiameterMm = 100.0;                               // 最大直径100mm
    toolChangerCfg.maxToolLengthMm = 300.0;                                 // 最大长度300mm

    // 填充通道配置 (对于固高，通常简化为单个主通道)
    if (config.numberOfChannels > 0) {
        config.channelsConfigs.resize(config.numberOfChannels);
        for (int i = 0; i < config.numberOfChannels; ++i) {
            ChannelConfig& chCfg = config.channelsConfigs[i];
            chCfg.name = "Channel" + std::to_string(i);
            // 映射该通道的轴 (在固高单通道模型中，所有轴都属于这个通道)
            for (int axIdx = 0; axIdx < config.totalNumberOfAxes; ++axIdx) {
                CncAxisConfig& axisCfg = config.axesConfigs[axIdx];
                chCfg.axes.push_back({axIdx, axisCfg.name});
            }
            // 映射该通道的主轴
            for (int spIdx = 0; spIdx < config.totalNumberOfSpindles; ++spIdx) {
                SpindleConfig& spindleCfg = config.spindlesConfigs[spIdx];
                chCfg.spindles.push_back({spIdx, spindleCfg.name});
            }
        }
    }

    // 填充支持的工件坐标系列表
    config.supportedWorkOffsets.clear();

    // 标准工件坐标系 G54-G59
    for (int i = 54; i <= 59; ++i) {
        WorkOffsetInfo workOffset;
        workOffset.name = "G" + std::to_string(i);
        workOffset.index = i - 54;        // 0-5
        workOffset.isActive = (i == 54);  // G54默认激活
        workOffset.isReadOnly = false;
        workOffset.description = "标准工件坐标系 " + workOffset.name;
        config.supportedWorkOffsets.push_back(workOffset);
    }

    // 填充宏变量范围配置（基于Googol SDK调查结果）
    config.macroVariableRange.minUserVarIndex = m_sysParamPtr.m_shm32Ptr->m_nUserVarShareMinNo;
    config.macroVariableRange.maxUserVarIndex = m_sysParamPtr.m_shm32Ptr->m_nUserVarShareMaxNo;

    // 可以根据实际的Googol系统参数来动态设置范围
    // 这里使用默认值，实际部署时可能需要从系统参数中读取

    m_lastError = "";
    return ErrorCode::Success;
}

// --- 配置参数读取相关方法 ---
ErrorCode GoogolCncInterface::getSystemConfig(SystemConfig& config) {
    ErrorCode initCheck = checkInitializedAndPointers();
    if (initCheck != ErrorCode::Success) return initCheck;

    config = m_systemConfig;
    return ErrorCode::Success;
}

// --- 系统配置管理方法 ---
ErrorCode GoogolCncInterface::getConfiguration(std::vector<ConfigCategory>& rootCategories) {
    if (m_isConfigInitialized) {
        ErrorCode err = getConfigurationImpl(rootCategories);
        if (err != ErrorCode::Success) {
            HW_LOG_ERROR(m_logger, "getConfiguration: getConfigurationImpl failed, return error: %d", (int)err);
            return err;
        }
        return ErrorCode::Success;
    }

    m_isConfigInitialized = true;

    // 尝试从JSON文件读取之前保存的配置进行恢复
    std::string jsonContent;
    bool loadSuccess = false;

    // 首先尝试加载主配置文件
    if (loadConfigFromFile(m_writablePath + "/syscfg_primary.json", jsonContent)) {
        if (parseJsonAndValidateChecksum(jsonContent, rootCategories)) {
            loadSuccess = true;
            HW_LOG_INFO(m_logger, "Successfully loaded configuration from primary JSON file");
        }
    }

    // 如果主文件失败，尝试备份文件
    if (!loadSuccess && loadConfigFromFile(m_writablePath + "/syscfg_backup.json", jsonContent)) {
        if (parseJsonAndValidateChecksum(jsonContent, rootCategories)) {
            loadSuccess = true;
            HW_LOG_INFO(m_logger, "Successfully loaded configuration from backup JSON file");
        }
    }

    if (!loadSuccess) {
        HW_LOG_INFO(m_logger, "No valid JSON configuration found, using default configuration");

        getConfigurationImpl(rootCategories);

        // 使用统一的Qt JSON序列化方法保存默认配置
        QJsonArray dataArray = configCategoriesToQJsonArray(rootCategories);
        QJsonDocument dataDoc(dataArray);
        QString dataJsonStr = dataDoc.toJson(QJsonDocument::Compact);
        std::string jsonStr = dataJsonStr.toStdString();
        std::string checksum = calculateChecksum(jsonStr);

        QJsonObject mainObj;
        mainObj["checksum"] = QString::fromStdString(checksum);
        mainObj["data"] = dataArray;

        QJsonDocument finalDoc(mainObj);
        QString finalJsonStr = finalDoc.toJson(QJsonDocument::Indented);
        std::string finalJson = finalJsonStr.toStdString();

        saveConfigToFile(m_writablePath + "/syscfg_primary.json", finalJson);
        saveConfigToFile(m_writablePath + "/syscfg_backup.json", finalJson);
    }
    setConfigurationImpl(rootCategories);

    HW_LOG_INFO(m_logger, "getConfiguration success");
    return ErrorCode::Success;
}

void GoogolCncInterface::printConfigCategory(const ConfigCategory& cat) const {
    HW_LOG_INFO(m_logger, "categoryId:%s", cat.categoryId.c_str());
    HW_LOG_INFO(m_logger, "categoryName:%s", cat.categoryName.c_str());
    // HW_LOG_INFO(m_logger, "categoryDescription:%s", cat.categoryDescription.c_str());
    for (const auto& node : cat.configNodes) {
        HW_LOG_INFO(m_logger, "nodeId:%s", node.id.c_str());
        HW_LOG_INFO(m_logger, "nodeName:%s", node.name.c_str());
        HW_LOG_INFO(m_logger, "nodeDescription:%s", node.description.c_str());

        // 将 ConfigValue variant 转换为字符串用于日志输出
        auto configValueToString = [](const ConfigValue& value) -> std::string {
            if (std::holds_alternative<std::string>(value)) {
                return std::get<std::string>(value);
            } else if (std::holds_alternative<int32_t>(value)) {
                return std::to_string(std::get<int32_t>(value));
            } else if (std::holds_alternative<int64_t>(value)) {
                return std::to_string(std::get<int64_t>(value));
            } else if (std::holds_alternative<double>(value)) {
                return std::to_string(std::get<double>(value));
            } else if (std::holds_alternative<bool>(value)) {
                return std::get<bool>(value) ? "true" : "false";
            } else {
                return "unknown";
            }
        };

        HW_LOG_INFO(m_logger, "nodeCurrentValue:%s", configValueToString(node.currentValue).c_str());

#if 0
        HW_LOG_INFO(m_logger, "nodeDefaultValue:%s", configValueToString(node.defaultValue).c_str());
        HW_LOG_INFO(m_logger, "nodeUnit:%s", node.unit.c_str());
        HW_LOG_INFO(m_logger, "nodeIsReadOnly:%d", node.isReadOnly ? 1 : 0);
        if (std::holds_alternative<EnumRange>(node.range)) {
            const EnumRange& er = std::get<EnumRange>(node.range);
            HW_LOG_INFO(m_logger, "nodeRangeEnumValues:%s", std::to_string(er.size()).c_str());
            for (const auto& ev : er) {
                HW_LOG_INFO(m_logger, "nodeRangeEnumValue:%s", ev.displayName.c_str());

                // 将 ConfigValue variant 转换为字符串用于日志输出
                std::string actualValueStr;
                if (std::holds_alternative<std::string>(ev.actualValue)) {
                    actualValueStr = std::get<std::string>(ev.actualValue);
                } else if (std::holds_alternative<int32_t>(ev.actualValue)) {
                    actualValueStr = std::to_string(std::get<int32_t>(ev.actualValue));
                } else if (std::holds_alternative<int64_t>(ev.actualValue)) {
                    actualValueStr = std::to_string(std::get<int64_t>(ev.actualValue));
                } else if (std::holds_alternative<double>(ev.actualValue)) {
                    actualValueStr = std::to_string(std::get<double>(ev.actualValue));
                } else if (std::holds_alternative<bool>(ev.actualValue)) {
                    actualValueStr = std::get<bool>(ev.actualValue) ? "true" : "false";
                } else {
                    actualValueStr = "unknown";
                }
                HW_LOG_INFO(m_logger, "nodeRangeEnumValueDescription:%s", actualValueStr.c_str());
            }
        }
#endif
    }
    for (const auto& sub : cat.subCategories) {
        printConfigCategory(sub);
    }
}

ErrorCode GoogolCncInterface::setConfiguration(const std::vector<ConfigCategory>& rootCategoriesToUpdate) {
    // TODO: 保存配置到文件
    if (rootCategoriesToUpdate.empty()) {
        HW_LOG_ERROR(m_logger, "setConfiguration rootCategoriesToUpdate is empty");
        return ErrorCode::OperationFailed;
    }

    setConfigurationImpl(rootCategoriesToUpdate);

    std::vector<ConfigCategory> categoriesToGet;
    if (getConfigurationImpl(categoriesToGet) != ErrorCode::Success) {
        HW_LOG_ERROR(m_logger, "setConfiguration getConfigurationImpl failed");
        return ErrorCode::OperationFailed;
    }

    // 将rootCategoriesToUpdate中的配置更新到categoriesToGet中
    // updateConfigCategories(rootCategoriesToUpdate, categoriesToGet);

    // 使用Qt JSON API统一序列化方法
    QJsonArray dataArray = configCategoriesToQJsonArray(categoriesToGet);
    QJsonDocument dataDoc(dataArray);
    QString dataJsonStr = dataDoc.toJson(QJsonDocument::Compact);
    std::string jsonStr = dataJsonStr.toStdString();
    std::string checksum = calculateChecksum(jsonStr);

    // 创建带checksum的JSON对象
    QJsonObject mainObj;
    mainObj["checksum"] = QString::fromStdString(checksum);
    mainObj["data"] = dataArray;

    QJsonDocument finalDoc(mainObj);
    QString finalJsonStr = finalDoc.toJson(QJsonDocument::Indented);
    std::string finalJson = finalJsonStr.toStdString();

    // 保存两份相同的JSON文件
    if (!saveConfigToFile(m_writablePath + "/syscfg_primary.json", finalJson) ||
        !saveConfigToFile(m_writablePath + "/syscfg_backup.json", finalJson)) {
        HW_LOG_ERROR(m_logger, "setConfiguration saveConfigToFile failed");
        return ErrorCode::OperationFailed;
    }

    return ErrorCode::Success;
}

// --- JSON转换和文件操作辅助函数 ---

bool GoogolCncInterface::ensureDirectoryExists(const std::string& path) {
    if (path.empty()) {
        return false;
    }

    // 检查路径是否已经存在
    struct stat st;
    if (stat(path.c_str(), &st) == 0) {
        // 路径存在，检查是否是目录
        return S_ISDIR(st.st_mode);
    }

    // 路径不存在，创建目录
    // 先找到父目录
    size_t lastSlash = path.find_last_of('/');
    if (lastSlash != std::string::npos && lastSlash > 0) {
        std::string parentPath = path.substr(0, lastSlash);
        // 递归创建父目录
        if (!ensureDirectoryExists(parentPath)) {
            return false;
        }
    }

    // 创建当前目录
    if (mkdir(path.c_str(), 0755) != 0) {
        // 如果创建失败，检查是否是因为目录已存在
        if (errno == EEXIST) {
            // 再次检查确认是目录
            if (stat(path.c_str(), &st) == 0 && S_ISDIR(st.st_mode)) {
                return true;
            }
        }
        return false;
    }

    return true;
}

std::string GoogolCncInterface::extractDirectoryFromPath(const std::string& filepath) {
    size_t lastSlash = filepath.find_last_of('/');
    if (lastSlash != std::string::npos) {
        return filepath.substr(0, lastSlash);
    }
    return "";  // 没有目录分隔符，返回空字符串
}

std::string GoogolCncInterface::escapeJsonString(const std::string& str) {
    std::string escaped;
    escaped.reserve(str.length() + 20);  // 预留一些空间用于转义字符

    for (size_t i = 0; i < str.length(); ++i) {
        unsigned char c = static_cast<unsigned char>(str[i]);

        switch (c) {
            case '"':
                escaped += "\\\"";
                break;
            case '\\':
                escaped += "\\\\";
                break;
            case '\b':
                escaped += "\\b";
                break;
            case '\f':
                escaped += "\\f";
                break;
            case '\n':
                escaped += "\\n";
                break;
            case '\r':
                escaped += "\\r";
                break;
            case '\t':
                escaped += "\\t";
                break;
            default:
                // 只对控制字符（0x00-0x1F）进行Unicode转义
                // 对于UTF-8字符（包括高位字节0x80-0xFF），直接保留
                if (c < 0x20) {
                    char hexBuf[8];
                    std::sprintf(hexBuf, "\\u%04x", c);
                    escaped += hexBuf;
                } else {
                    // 直接添加字符，保持UTF-8编码
                    escaped += static_cast<char>(c);
                }
                break;
        }
    }
    return escaped;
}

std::string GoogolCncInterface::configValueToJsonString(const ConfigValue& value) {
    if (std::holds_alternative<bool>(value)) {
        return std::get<bool>(value) ? "true" : "false";
    } else if (std::holds_alternative<int32_t>(value)) {
        return std::to_string(std::get<int32_t>(value));
    } else if (std::holds_alternative<int64_t>(value)) {
        return std::to_string(std::get<int64_t>(value));
    } else if (std::holds_alternative<double>(value)) {
        std::ostringstream oss;
        oss << std::fixed << std::setprecision(6) << std::get<double>(value);
        return oss.str();
    } else if (std::holds_alternative<std::string>(value)) {
        return "\"" + escapeJsonString(std::get<std::string>(value)) + "\"";
    }
    return "null";
}

std::string GoogolCncInterface::configRangeToJsonString(const ConfigRange& range) {
    if (std::holds_alternative<std::monostate>(range)) {
        return "null";
    } else if (std::holds_alternative<IntRange>(range)) {
        const IntRange& ir = std::get<IntRange>(range);
        return "{\"type\":\"IntRange\",\"min\":" + std::to_string(ir.min) + ",\"max\":" + std::to_string(ir.max) +
               ",\"step\":" + std::to_string(ir.step) + "}";
    } else if (std::holds_alternative<DoubleRange>(range)) {
        const DoubleRange& dr = std::get<DoubleRange>(range);
        std::ostringstream oss;
        oss << "{\"type\":\"DoubleRange\",\"min\":" << std::fixed << std::setprecision(6) << dr.min
            << ",\"max\":" << dr.max << ",\"step\":" << dr.step << "}";
        return oss.str();
    } else if (std::holds_alternative<EnumRange>(range)) {
        const EnumRange& er = std::get<EnumRange>(range);
        std::string result = "{\"type\":\"EnumRange\",\"options\":[";
        for (size_t i = 0; i < er.size(); ++i) {
            if (i > 0) result += ",";
            result += "{\"displayName\":\"" + escapeJsonString(er[i].displayName) +
                      "\",\"actualValue\":" + configValueToJsonString(er[i].actualValue) + "}";
        }
        result += "]}";
        return result;
    } else if (std::holds_alternative<StringRange>(range)) {
        const StringRange& sr = std::get<StringRange>(range);
        return "{\"type\":\"StringRange\",\"maxLength\":" + std::to_string(sr.maxLength) + ",\"regexPattern\":\"" +
               escapeJsonString(sr.regexPattern) + "\"}";
    }
    return "null";
}

std::string GoogolCncInterface::configNodeToJsonString(const ConfigNode& node) {
    std::string result = "{";
    result += "\"id\":\"" + escapeJsonString(node.id) + "\",";
    result += "\"name\":\"" + escapeJsonString(node.name) + "\",";
    result += "\"description\":\"" + escapeJsonString(node.description) + "\",";
    result += "\"semanticType\":\"" + std::to_string(static_cast<int>(node.semanticType)) + "\",";
    result += "\"currentValue\":" + configValueToJsonString(node.currentValue) + ",";
    result += "\"defaultValue\":" + configValueToJsonString(node.defaultValue) + ",";
    result += "\"range\":" + configRangeToJsonString(node.range) + ",";
    result += "\"unit\":\"" + escapeJsonString(node.unit) + "\",";
    result += "\"isReadOnly\":";
    result += (node.isReadOnly ? "true" : "false");
    result += "}";
    return result;
}

std::string GoogolCncInterface::configCategoryToJsonString(const ConfigCategory& category) {
    std::string result = "{";
    result += "\"categoryId\":\"" + escapeJsonString(category.categoryId) + "\",";
    result += "\"categoryName\":\"" + escapeJsonString(category.categoryName) + "\",";
    result += "\"categoryDescription\":\"" + escapeJsonString(category.categoryDescription) + "\",";

    // 配置节点
    result += "\"configNodes\":[";
    for (size_t i = 0; i < category.configNodes.size(); ++i) {
        if (i > 0) result += ",";
        result += configNodeToJsonString(category.configNodes[i]);
    }
    result += "],";

    // 子分类
    result += "\"subCategories\":[";
    for (size_t i = 0; i < category.subCategories.size(); ++i) {
        if (i > 0) result += ",";
        result += configCategoryToJsonString(category.subCategories[i]);
    }
    result += "]";

    result += "}";
    return result;
}

std::string GoogolCncInterface::configCategoriesToJson(const std::vector<ConfigCategory>& rootCategories) {
    std::string result = "[";
    for (size_t i = 0; i < rootCategories.size(); ++i) {
        if (i > 0) result += ",";
        result += configCategoryToJsonString(rootCategories[i]);
    }
    result += "]";
    return result;
}

QJsonArray GoogolCncInterface::configCategoriesToQJsonArray(const std::vector<ConfigCategory>& rootCategories) {
    QJsonArray categoriesArray;

    for (const auto& category : rootCategories) {
        QJsonObject categoryObj = configCategoryToQJsonObject(category);
        categoriesArray.append(categoryObj);
    }

    return categoriesArray;
}

QJsonObject GoogolCncInterface::configCategoryToQJsonObject(const ConfigCategory& category) {
    QJsonObject categoryObj;

    categoryObj["categoryId"] = QString::fromStdString(category.categoryId);
    categoryObj["categoryName"] = QString::fromStdString(category.categoryName);
    categoryObj["categoryDescription"] = QString::fromStdString(category.categoryDescription);

    // 配置节点
    QJsonArray configNodesArray;
    for (const auto& node : category.configNodes) {
        QJsonObject nodeObj = configNodeToQJsonObject(node);
        configNodesArray.append(nodeObj);
    }
    categoryObj["configNodes"] = configNodesArray;

    // 子分类
    QJsonArray subCategoriesArray;
    for (const auto& subCategory : category.subCategories) {
        QJsonObject subCategoryObj = configCategoryToQJsonObject(subCategory);
        subCategoriesArray.append(subCategoryObj);
    }
    categoryObj["subCategories"] = subCategoriesArray;

    return categoryObj;
}

QJsonObject GoogolCncInterface::configNodeToQJsonObject(const ConfigNode& node) {
    QJsonObject nodeObj;

    nodeObj["id"] = QString::fromStdString(node.id);
    nodeObj["name"] = QString::fromStdString(node.name);
    nodeObj["description"] = QString::fromStdString(node.description);
    nodeObj["semanticType"] = QString::number(static_cast<int>(node.semanticType));
    nodeObj["currentValue"] = configValueToQJsonValue(node.currentValue);
    nodeObj["defaultValue"] = configValueToQJsonValue(node.defaultValue);
    nodeObj["range"] = configRangeToQJsonObject(node.range);
    nodeObj["unit"] = QString::fromStdString(node.unit);
    nodeObj["isReadOnly"] = node.isReadOnly;

    return nodeObj;
}

QJsonValue GoogolCncInterface::configValueToQJsonValue(const ConfigValue& value) {
    if (std::holds_alternative<bool>(value)) {
        return std::get<bool>(value);
    } else if (std::holds_alternative<int32_t>(value)) {
        return std::get<int32_t>(value);
    } else if (std::holds_alternative<int64_t>(value)) {
        return static_cast<qint64>(std::get<int64_t>(value));
    } else if (std::holds_alternative<double>(value)) {
        return std::get<double>(value);
    } else if (std::holds_alternative<std::string>(value)) {
        return QString::fromStdString(std::get<std::string>(value));
    }
    return QJsonValue::Null;
}

QJsonObject GoogolCncInterface::configRangeToQJsonObject(const ConfigRange& range) {
    QJsonObject rangeObj;

    if (std::holds_alternative<std::monostate>(range)) {
        return QJsonObject();  // 返回空的JSON对象
    } else if (std::holds_alternative<IntRange>(range)) {
        const IntRange& ir = std::get<IntRange>(range);
        rangeObj["type"] = "IntRange";
        rangeObj["min"] = static_cast<qint64>(ir.min);
        rangeObj["max"] = static_cast<qint64>(ir.max);
        rangeObj["step"] = static_cast<qint64>(ir.step);
    } else if (std::holds_alternative<DoubleRange>(range)) {
        const DoubleRange& dr = std::get<DoubleRange>(range);
        rangeObj["type"] = "DoubleRange";
        rangeObj["min"] = dr.min;
        rangeObj["max"] = dr.max;
        rangeObj["step"] = dr.step;
    } else if (std::holds_alternative<EnumRange>(range)) {
        const EnumRange& er = std::get<EnumRange>(range);
        rangeObj["type"] = "EnumRange";
        QJsonArray optionsArray;
        for (const auto& enumOption : er) {
            QJsonObject optionObj;
            optionObj["displayName"] = QString::fromStdString(enumOption.displayName);
            optionObj["actualValue"] = configValueToQJsonValue(enumOption.actualValue);
            optionsArray.append(optionObj);
        }
        rangeObj["options"] = optionsArray;
    } else if (std::holds_alternative<StringRange>(range)) {
        const StringRange& sr = std::get<StringRange>(range);
        rangeObj["type"] = "StringRange";
        rangeObj["maxLength"] = static_cast<qint64>(sr.maxLength);
        rangeObj["regexPattern"] = QString::fromStdString(sr.regexPattern);
    }

    return rangeObj;
}

std::string GoogolCncInterface::calculateChecksum(const std::string& data) {
    // 简单的checksum计算 - 使用CRC32或者简单的hash
    std::hash<std::string> hasher;
    size_t hashValue = hasher(data);

    // 转换为十六进制字符串
    std::ostringstream oss;
    oss << std::hex << hashValue;
    return oss.str();
}

bool GoogolCncInterface::saveConfigToFile(const std::string& filename, const std::string& jsonContent) {
    try {
        // 从文件路径提取目录
        std::string directory = extractDirectoryFromPath(filename);

        // 如果有目录部分，确保目录存在
        if (!directory.empty()) {
            if (!ensureDirectoryExists(directory)) {
                HW_LOG_ERROR(m_logger, "Failed to create directory: %s", directory.c_str());
                return false;
            }
        }

        // 创建并写入文件
        std::ofstream file(filename);
        if (!file.is_open()) {
            HW_LOG_ERROR(m_logger, "Failed to open file for writing: %s", filename.c_str());
            return false;
        }

        file << jsonContent;
        file.close();

        HW_LOG_DEBUG(m_logger, "Successfully saved config to file: %s", filename.c_str());
        return true;
    } catch (const std::exception& e) {
        HW_LOG_ERROR(m_logger, "Exception while saving config file: %s", e.what());
        return false;
    }
}

bool GoogolCncInterface::loadConfigFromFile(const std::string& filename, std::string& jsonContent) {
    try {
        std::ifstream file(filename);
        if (!file.is_open()) {
            return false;
        }

        std::stringstream buffer;
        buffer << file.rdbuf();
        jsonContent = buffer.str();
        file.close();

        return true;
    } catch (const std::exception& e) {
        return false;
    }
}

bool GoogolCncInterface::parseConfigCategoryFromJson(const json& categoryObj, ConfigCategory& category) {
    // 解析基本信息
    if (categoryObj.contains("categoryId") && categoryObj["categoryId"].is_string()) {
        category.categoryId = categoryObj["categoryId"].get<std::string>();
    } else {
        HW_LOG_INFO(m_logger, "parseConfigCategoryFromJson: categoryId field not found or not string");
    }

    if (categoryObj.contains("categoryName") && categoryObj["categoryName"].is_string()) {
        category.categoryName = categoryObj["categoryName"].get<std::string>();
    } else {
        HW_LOG_INFO(m_logger, "parseConfigCategoryFromJson: categoryName field not found or not string");
    }

    if (categoryObj.contains("categoryDescription") && categoryObj["categoryDescription"].is_string()) {
        category.categoryDescription = categoryObj["categoryDescription"].get<std::string>();
    }

    // 解析配置节点
    if (categoryObj.contains("configNodes") && categoryObj["configNodes"].is_array()) {
        const auto& nodesArray = categoryObj["configNodes"];
        for (const auto& nodeValue : nodesArray) {
            if (nodeValue.is_object()) {
                ConfigNode node;
                if (parseConfigNodeFromJson(nodeValue, node)) {
                    category.configNodes.push_back(node);
                } else {
                }
            } else {
            }
        }
    } else {
    }

    // 解析子分类
    if (categoryObj.contains("subCategories") && categoryObj["subCategories"].is_array()) {
        const auto& subCategoriesArray = categoryObj["subCategories"];
        for (const auto& subCategoryValue : subCategoriesArray) {
            if (subCategoryValue.is_object()) {
                ConfigCategory subCategory;
                if (parseConfigCategoryFromJson(subCategoryValue, subCategory)) {
                    category.subCategories.push_back(subCategory);
                } else {
                }
            } else {
            }
        }
    } else {
    }

    bool result = !category.categoryId.empty() && !category.categoryName.empty();
    return result;
}

bool GoogolCncInterface::parseConfigNodeFromJson(const json& nodeObj, ConfigNode& node) {
    // 解析基本字符串字段
    if (nodeObj.contains("id") && nodeObj["id"].is_string()) {
        node.id = nodeObj["id"].get<std::string>();
    }
    if (nodeObj.contains("name") && nodeObj["name"].is_string()) {
        node.name = nodeObj["name"].get<std::string>();
    }
    if (nodeObj.contains("description") && nodeObj["description"].is_string()) {
        node.description = nodeObj["description"].get<std::string>();
    }
    if (nodeObj.contains("unit") && nodeObj["unit"].is_string()) {
        node.unit = nodeObj["unit"].get<std::string>();
    }

    // 解析语义类型
    if (nodeObj.contains("semanticType")) {
        if (nodeObj["semanticType"].is_string()) {
            int typeValue = std::stoi(nodeObj["semanticType"].get<std::string>());
            node.semanticType = static_cast<ConfigSemanticType>(typeValue);
        } else if (nodeObj["semanticType"].is_number()) {
            int typeValue = nodeObj["semanticType"].get<int>();
            node.semanticType = static_cast<ConfigSemanticType>(typeValue);
        }
    }

    // 解析布尔字段
    if (nodeObj.contains("isReadOnly") && nodeObj["isReadOnly"].is_boolean()) {
        node.isReadOnly = nodeObj["isReadOnly"].get<bool>();
    }

    // 解析当前值和默认值
    if (nodeObj.contains("currentValue")) {
        node.currentValue = parseConfigValueFromJson(nodeObj["currentValue"]);
    } else {
    }

    if (nodeObj.contains("defaultValue")) {
        node.defaultValue = parseConfigValueFromJson(nodeObj["defaultValue"]);
    } else {
    }

    // 解析范围
    if (nodeObj.contains("range") && nodeObj["range"].is_object()) {
        node.range = parseConfigRangeFromJson(nodeObj["range"]);
    } else {
    }

    bool result = !node.id.empty() && !node.name.empty();
    return result;
}

ConfigValue GoogolCncInterface::parseConfigValueFromJson(const json& jsonValue) {
    // 检查是否为null或undefined
    if (jsonValue.is_null()) {
        return std::string("");
    }

    // 检查是否为空字符串
    if (jsonValue.is_string() && jsonValue.get<std::string>().empty()) {
        return std::string("");
    }

    if (jsonValue.is_boolean()) {
        // return jsonValue.get<bool>();
        // Bug: 这里返回bool类型，在setConfiguration时，会转换为int32_t，导致类型不匹配
        return static_cast<int32_t>(jsonValue.get<bool>());
    } else if (jsonValue.is_number()) {
        if (jsonValue.is_number_integer()) {
            int64_t val = jsonValue.get<int64_t>();
            return val;
        } else {
            double val = jsonValue.get<double>();
            return val;
        }
    } else if (jsonValue.is_string()) {
        return jsonValue.get<std::string>();
    }

    // 默认返回空字符串
    return std::string("");
}

ConfigRange GoogolCncInterface::parseConfigRangeFromJson(const json& rangeObj) {
    if (!rangeObj.contains("type") || !rangeObj["type"].is_string()) {
        return std::monostate{};
    }

    std::string type = rangeObj["type"].get<std::string>();

    if (type == "IntRange") {
        IntRange range;
        if (rangeObj.contains("min")) range.min = rangeObj["min"].get<int64_t>();
        if (rangeObj.contains("max")) range.max = rangeObj["max"].get<int64_t>();
        if (rangeObj.contains("step")) range.step = rangeObj["step"].get<int64_t>();
        return range;
    } else if (type == "DoubleRange") {
        DoubleRange range;
        if (rangeObj.contains("min")) range.min = rangeObj["min"].get<double>();
        if (rangeObj.contains("max")) range.max = rangeObj["max"].get<double>();
        if (rangeObj.contains("step")) range.step = rangeObj["step"].get<double>();
        return range;
    } else if (type == "EnumRange") {
        EnumRange enumRange;
        if (rangeObj.contains("options") && rangeObj["options"].is_array()) {
            const auto& optionsArray = rangeObj["options"];
            for (const auto& optionValue : optionsArray) {
                if (optionValue.is_object()) {
                    EnumValueOption option;
                    if (optionValue.contains("displayName") && optionValue["displayName"].is_string()) {
                        option.displayName = optionValue["displayName"].get<std::string>();
                    }
                    if (optionValue.contains("actualValue")) {
                        option.actualValue = parseConfigValueFromJson(optionValue["actualValue"]);
                    }
                    enumRange.push_back(option);
                }
            }
        }
        return enumRange;
    } else if (type == "StringRange") {
        StringRange range;
        if (rangeObj.contains("maxLength")) range.maxLength = rangeObj["maxLength"].get<size_t>();
        if (rangeObj.contains("regexPattern")) range.regexPattern = rangeObj["regexPattern"].get<std::string>();
        return range;
    }

    return std::monostate{};
}

bool GoogolCncInterface::parseJsonToConfigCategories(const std::string& jsonArrayStr,
                                                     std::vector<ConfigCategory>& rootCategories) {
    try {  // 使用 nlohmann/json 解析器
        json j = json::parse(jsonArrayStr);

        if (!j.is_array()) {
            return false;
        }
        for (const auto& categoryValue : j) {
            if (!categoryValue.is_object()) {
                continue;
            }

            ConfigCategory category;
            bool ok = parseConfigCategoryFromJson(categoryValue, category);
            if (ok) {
                rootCategories.push_back(category);
            } else {
            }
        }

        return !rootCategories.empty();

    } catch (const json::exception& e) {
        return false;
    }
}

bool GoogolCncInterface::parseJsonAndValidateChecksum(const std::string& jsonContent,
                                                      std::vector<ConfigCategory>& rootCategories) {
    try {
        // 使用 nlohmann/json 解析主JSON结构
        json doc = json::parse(jsonContent);

        if (!doc.is_object()) {
            return false;
        }

        // 检查必要字段
        if (!doc.contains("checksum") || !doc.contains("data")) {
            return false;
        }

        if (!doc["checksum"].is_string() || !doc["data"].is_array()) {
            return false;
        }

        // 提取checksum和data
        std::string storedChecksum = doc["checksum"].get<std::string>();
        const auto& dataArray = doc["data"];
        // 转换data数组为字符串用于checksum验证
        std::string dataJson = dataArray.dump();

        // 验证checksum (已统一JSON序列化方法)
        std::string calculatedChecksum = calculateChecksum(dataJson);
        if (calculatedChecksum != storedChecksum) {
            return false;
        }

        // 如果checksum验证通过，直接解析并使用JSON数据
        if (parseJsonToConfigCategories(dataJson, rootCategories)) {
            return true;
        } else {
            return false;
        }

    } catch (const json::exception& e) {
        return false;
    }
}

// 新增：递归同步配置的辅助函数
void GoogolCncInterface::updateConfigCategories(const std::vector<ConfigCategory>& from,
                                                std::vector<ConfigCategory>& to) {
    for (const auto& fromCat : from) {
        // 在 to 中查找同 categoryId 的分类
        auto itToCat = std::find_if(to.begin(), to.end(),
                                    [&](ConfigCategory& toCat) { return toCat.categoryId == fromCat.categoryId; });
        if (itToCat != to.end()) {
            // 更新 configNodes
            for (const auto& fromNode : fromCat.configNodes) {
                auto itToNode = std::find_if(itToCat->configNodes.begin(), itToCat->configNodes.end(),
                                             [&](ConfigNode& toNode) { return toNode.id == fromNode.id; });
                if (itToNode != itToCat->configNodes.end()) {
                    itToNode->currentValue = fromNode.currentValue;
                }
            }
            // 递归更新子分类
            updateConfigCategories(fromCat.subCategories, itToCat->subCategories);
        }
    }
}