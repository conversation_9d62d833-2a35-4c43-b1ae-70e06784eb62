#include <QDateTime>
#include <QDir>
#include <QJsonArray>
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonValue>
#include <chrono>
#include <cstring>
#include <map>
#include <random>
#include "tools/ToolManagerSimple.h"
#include <string>

#include "CncGlobalParamDll.h"
#include "GoogolCncInterface.h"
#include "ParaDef.h"

// 刀具数据文件版本号
constexpr const char* TOOL_DATA_VERSION = "1.2";

// --- 刀具管理相关方法 ---

ErrorCode GoogolCncInterface::getCurrentToolInfo(int channelId, ToolInfo& toolInfo) {
    if (!m_toolManager) {
        toolInfo = {};
        toolInfo.isValid = false;
        m_lastError = "getCurrentToolInfo 错误: 刀具管理器未初始化";
        return ErrorCode::NotInitialized;
    }
    return m_toolManager->getCurrentToolInfo(channelId, toolInfo);
}

ErrorCode GoogolCncInterface::getToolParameters(const std::string& uuid, ToolInfo& toolInfo) {
    if (!m_toolManager) {
        toolInfo = {};
        toolInfo.isValid = false;
        m_lastError = "getToolParameters 错误: 刀具管理器未初始化";
        return ErrorCode::NotInitialized;
    }
    return m_toolManager->getToolParameters(uuid, toolInfo);
}

ErrorCode GoogolCncInterface::setToolParameters(ToolInfo& toolInfo) {
    if (!m_toolManager) {
        m_lastError = "setToolParameters 错误: 刀具管理器未初始化";
        return ErrorCode::NotInitialized;
    }
    return m_toolManager->setToolParameters(toolInfo);
}

ErrorCode GoogolCncInterface::getToolInfoInPocket(int toolChangerId, int pocketNumber, ToolInfo& toolInfo) {
    if (!m_toolManager) {
        toolInfo = {};
        toolInfo.isValid = false;
        m_lastError = "getToolInfoInPocket 错误: 刀具管理器未初始化";
        return ErrorCode::NotInitialized;
    }
    return m_toolManager->getToolInfoInPocket(toolChangerId, pocketNumber, toolInfo);
}

ErrorCode GoogolCncInterface::loadToolIntoPocket(int toolChangerId, int pocketNumber, int toolNumber) {
    if (!m_toolManager) {
        m_lastError = "loadToolIntoPocket 错误: 刀具管理器未初始化";
        return ErrorCode::NotInitialized;
    }
    return m_toolManager->loadToolIntoPocket(toolChangerId, pocketNumber, toolNumber);
}

ErrorCode GoogolCncInterface::unloadToolFromPocket(int toolChangerId, int pocketNumber, int& unloadedToolNumber) {
    if (!m_toolManager) {
        unloadedToolNumber = 0;
        m_lastError = "unloadToolFromPocket 错误: 刀具管理器未初始化";
        return ErrorCode::NotInitialized;
    }
    return m_toolManager->unloadToolFromPocket(toolChangerId, pocketNumber, unloadedToolNumber);
}

ErrorCode GoogolCncInterface::exchangeToolsInPockets(int toolChangerId, int pocketNumber1, int pocketNumber2) {
    if (!m_toolManager) {
        m_lastError = "exchangeToolsInPockets 错误: 刀具管理器未初始化";
        return ErrorCode::NotInitialized;
    }
    return m_toolManager->exchangeToolsInPockets(toolChangerId, pocketNumber1, pocketNumber2);
}

ErrorCode GoogolCncInterface::moveToolFromPocketToSpindle(int channelId, int toolChangerId, int pocketNumber,
                                                          int spindleIndex) {
    // 这个方法涉及主轴状态管理，暂时返回OperationFailed
    m_lastError = "moveToolFromPocketToSpindle 暂未实现";
    return ErrorCode::OperationFailed;
}

ErrorCode GoogolCncInterface::moveToolFromSpindleToPocket(int channelId, int spindleIndex, int toolChangerId,
                                                          int pocketNumber, int* movedToPocketToolNumber) {
    // 这个方法涉及主轴状态管理，暂时返回OperationFailed
    if (movedToPocketToolNumber) *movedToPocketToolNumber = 0;
    m_lastError = "moveToolFromSpindleToPocket 暂未实现";
    return ErrorCode::OperationFailed;
}

ErrorCode GoogolCncInterface::getMagazineStatus(int toolChangerId, std::vector<PocketStatus>& pocketStatuses) {
    // 这个方法涉及复杂的刀库状态管理，暂时返回OperationFailed
    pocketStatuses.clear();
    m_lastError = "getMagazineStatus 暂未实现";
    return ErrorCode::OperationFailed;
}

ErrorCode GoogolCncInterface::getAllToolParameters(std::map<std::string, ToolInfo>& allToolsInfo) {
    if (!m_toolManager) {
        m_lastError = "getAllToolParameters 错误: 刀具管理器未初始化";
        return ErrorCode::NotInitialized;
    }
    return m_toolManager->getAllToolParameters(allToolsInfo);
}

ErrorCode GoogolCncInterface::getToolsByMagazine(int toolChangerId, std::vector<ToolInfo>& toolsInMagazine) {
    if (!m_toolManager) {
        m_lastError = "getToolsByMagazine 错误: 刀具管理器未初始化";
        return ErrorCode::NotInitialized;
    }
    return m_toolManager->getToolsByMagazine(toolChangerId, toolsInMagazine);
}

ErrorCode GoogolCncInterface::deleteTool(const std::string& uuid) {
    if (!m_toolManager) {
        m_lastError = "deleteTool 错误: 刀具管理器未初始化";
        return ErrorCode::NotInitialized;
    }
    return m_toolManager->deleteTool(uuid);
}

