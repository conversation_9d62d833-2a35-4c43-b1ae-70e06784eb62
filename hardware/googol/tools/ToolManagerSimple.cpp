#include "ToolManagerSimple.h"
#include "../GoogolCncInterface.h"
#include <algorithm>
#include <random>
#include <sstream>
#include <iomanip>
#include <fstream>
#include <QDir>
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>
#include <QStandardPaths>
#include <QDebug>

ToolManagerSimple::ToolManagerSimple(GoogolCncInterface& interface)
    : m_interface(interface), m_isInitialized(false) {
}

ErrorCode ToolManagerSimple::initialize() {
    std::lock_guard<std::mutex> lock(m_toolMutex);

    if (!m_interface.isInitialized()) {
        return ErrorCode::NotInitialized;
    }

    // 初始化系统刀库
    initializeSystemMagazine();

    // 加载刀具数据（从配置文件或默认数据）
    ErrorCode loadResult = loadToolDataFromConfig();
    if (loadResult != ErrorCode::Success) {
        // 如果加载失败，使用默认数据
        qDebug() << "加载刀具配置失败，使用默认数据";
        initializeToolData();
    }

    // 初始化H映射表
    initializeHMappingTable();

    m_isInitialized = true;
    return ErrorCode::Success;
}

ErrorCode ToolManagerSimple::getCurrentToolInfo(int channelId, ToolInfo& toolInfo) {
    if (channelId < 0 || channelId >= m_interface.m_loadedChannelsCount) {
        m_interface.m_lastError = "无效的通道ID: " + std::to_string(channelId);
        return ErrorCode::InvalidParam;
    }

    toolInfo = {};  // 初始化
    toolInfo.isValid = false;

    // 简化实现：返回默认刀具信息
    toolInfo.number = 1;
    toolInfo.dNumber = 1;
    toolInfo.name = "当前刀具";
    toolInfo.isValid = true;

    return ErrorCode::Success;
}

ErrorCode ToolManagerSimple::getToolParameters(const std::string& uuid, ToolInfo& toolInfo) {
    std::lock_guard<std::mutex> lock(m_toolMutex);

    if (!m_isInitialized) {
        return ErrorCode::NotInitialized;
    }

    auto it = m_toolParametersByUuid.find(uuid);
    if (it == m_toolParametersByUuid.end()) {
        m_interface.m_lastError = "getToolParameters 错误: 未找到 UUID " + uuid;
        return ErrorCode::InvalidParam;
    }

    toolInfo = it->second;
    return ErrorCode::Success;
}

ErrorCode ToolManagerSimple::setToolParameters(ToolInfo& toolInfo) {
    std::lock_guard<std::mutex> lock(m_toolMutex);

    if (!m_isInitialized) {
        return ErrorCode::NotInitialized;
    }

    // 如果 UUID 为空，说明是创建新刀具
    if (toolInfo.uuid.empty()) {
        toolInfo.uuid = generateUuid();
    }

    // 验证刀具信息
    if (toolInfo.name.empty()) {
        m_interface.m_lastError = "setToolParameters 错误: 刀具名称不能为空。";
        return ErrorCode::InvalidParam;
    }

    // 验证几何参数
    if (toolInfo.geometryRadius < 0 || toolInfo.geometryLengthZ < 0) {
        m_interface.m_lastError = "setToolParameters 错误: 几何参数不能为负数。";
        return ErrorCode::InvalidParam;
    }

    // 保存到主存储
    m_toolParametersByUuid[toolInfo.uuid] = toolInfo;

    // 更新刀号索引
    updateToolNumberIndex(toolInfo);

    // 更新H映射表
    updateHMappingTable();

    // 自动保存到配置文件
    ErrorCode saveResult = saveToolDataToConfig();
    if (saveResult != ErrorCode::Success) {
        qDebug() << "保存刀具配置失败，但刀具参数已更新";
    }

    return ErrorCode::Success;
}

ErrorCode ToolManagerSimple::deleteTool(const std::string& uuid) {
    std::lock_guard<std::mutex> lock(m_toolMutex);

    if (!m_isInitialized) {
        return ErrorCode::NotInitialized;
    }

    auto it = m_toolParametersByUuid.find(uuid);
    if (it == m_toolParametersByUuid.end()) {
        m_interface.m_lastError = "deleteTool 错误: 未找到 UUID " + uuid;
        return ErrorCode::InvalidParam;
    }

    ToolInfo toolInfo = it->second;

    // 从主存储中删除
    m_toolParametersByUuid.erase(it);

    // 从刀号索引中移除
    removeFromToolNumberIndex(uuid);

    // 更新H映射表
    updateHMappingTable();

    // 自动保存到配置文件
    ErrorCode saveResult = saveToolDataToConfig();
    if (saveResult != ErrorCode::Success) {
        qDebug() << "保存刀具配置失败，但刀具已删除";
    }

    return ErrorCode::Success;
}

ErrorCode ToolManagerSimple::getAllToolParameters(std::map<std::string, ToolInfo>& allToolsInfo) {
    std::lock_guard<std::mutex> lock(m_toolMutex);

    if (!m_isInitialized) {
        return ErrorCode::NotInitialized;
    }

    allToolsInfo = m_toolParametersByUuid;
    return ErrorCode::Success;
}

void ToolManagerSimple::initializeHMappingTable() {
    // 注意：此方法假设已经获得了m_toolMutex锁
    
    // 设置H映射表大小（最大100个H号）
    m_interface.m_macroPublicParamPtr[0].m_shmPtr->m_dPublicUserVar[20200 - 500] = 100;

    // 清空H映射表
    for (int i = 1; i <= 100; i++) {
        m_interface.m_macroPublicParamPtr[0].m_shmPtr->m_dPublicUserVar[20200 + i - 500] = 0;
    }

    // 根据当前刀具列表重建H映射表
    updateHMappingTable();
}

void ToolManagerSimple::updateHMappingTable() {
    // 注意：此方法假设已经获得了m_toolMutex锁

    // 清空H映射表
    for (int hIndex = 1; hIndex <= 100; hIndex++) {
        m_interface.m_macroPublicParamPtr[0].m_shmPtr->m_dPublicUserVar[20200 + hIndex - 500] = 0;
    }

    // 清空所有通道的ToolPara数组
    for (int channelId = 0; channelId < m_interface.m_loadedChannelsCount; ++channelId) {
        for (int i = 0; i < 100; i++) { // 假设最大100个H号
            TOOL_PARA_PTR& toolPara = m_interface.m_toolsParamPtr[channelId].m_shmPtr->m_ToolPara[i];
            memset(&toolPara, 0, sizeof(toolPara));
        }
    }

    // 清空H号映射缓存
    clearHMappingCache();

    int nextAvailableH = 1; // 下一个可用的H号

    // 遍历所有刀具，按刀号排序建立H映射
    std::map<int, std::vector<ToolInfo>> toolsByNumber;
    for (const auto& [uuid, tool] : m_toolParametersByUuid) {
        if (tool.isValid && tool.number > 0) {
            toolsByNumber[tool.number].push_back(tool);
        }
    }

    // 为每个刀号的每个刀沿分配H号并设置刀补数据
    for (const auto& [toolNumber, tools] : toolsByNumber) {
        // 按刀沿号排序
        std::vector<ToolInfo> sortedTools = tools;
        std::sort(sortedTools.begin(), sortedTools.end(),
                 [](const ToolInfo& a, const ToolInfo& b) {
                     return a.dNumber < b.dNumber;
                 });

        for (const auto& tool : sortedTools) {
            if (nextAvailableH > 100) {
                break;
            }

            // 计算刀号+刀沿的组合值（刀号*100+刀沿）
            int toolNumberAndEdge = toolNumber * 100 + tool.dNumber;

            // 设置H映射：H号 -> 刀号+刀沿
            m_interface.m_macroPublicParamPtr[0].m_shmPtr->m_dPublicUserVar[20200 + nextAvailableH - 500] = toolNumberAndEdge;

            // 更新H号映射缓存
            m_hNumberToUuidCache[nextAvailableH] = tool.uuid;
            m_uuidToHNumberCache[tool.uuid] = nextAvailableH;
            m_hNumberToToolNumberAndDCache[nextAvailableH] = toolNumberAndEdge;

            // 将刀补数据保存到对应H号索引的ToolPara中
            int hIndex = nextAvailableH - 1; // ToolPara数组索引从0开始，H号从1开始
            for (int channelId = 0; channelId < m_interface.m_loadedChannelsCount; ++channelId) {
                setToolToDeviceByHIndex(channelId, hIndex, tool);
            }

            nextAvailableH++;
        }
    }

    // 更新H映射表实际大小
    m_interface.m_macroPublicParamPtr[0].m_shmPtr->m_dPublicUserVar[20200 - 500] = nextAvailableH - 1;
}

int ToolManagerSimple::findHNumberForTool(int toolNumber, int dNumber) {
    std::lock_guard<std::mutex> lock(m_toolMutex);

    // 优先使用缓存进行快速查找
    int targetValue = toolNumber * 100 + dNumber;
    for (const auto& [hNumber, cachedValue] : m_hNumberToToolNumberAndDCache) {
        if (cachedValue == targetValue) {
            return hNumber;
        }
    }

    // 如果缓存中没有找到，回退到宏变量查找（兼容性保证）
    int mappingTableSize = static_cast<int>(m_interface.m_macroPublicParamPtr[0].m_shmPtr->m_dPublicUserVar[20200 - 500]);

    for (int hNumber = 1; hNumber <= mappingTableSize && hNumber <= 100; hNumber++) {
        int mappedValue = static_cast<int>(m_interface.m_macroPublicParamPtr[0].m_shmPtr->m_dPublicUserVar[20200 + hNumber - 500]);
        if (mappedValue == targetValue) {
            return hNumber;
        }
    }

    return 0; // 未找到
}

ErrorCode ToolManagerSimple::getToolInfoByHNumber(int hNumber, ToolInfo& toolInfo) {
    std::lock_guard<std::mutex> lock(m_toolMutex);

    if (!m_isInitialized) {
        return ErrorCode::NotInitialized;
    }

    if (hNumber <= 0 || hNumber > 100) {
        m_interface.m_lastError = "无效的H号: " + std::to_string(hNumber);
        return ErrorCode::InvalidParam;
    }

    // 优先使用缓存进行快速查找
    auto cacheIt = m_hNumberToUuidCache.find(hNumber);
    if (cacheIt != m_hNumberToUuidCache.end()) {
        auto toolIt = m_toolParametersByUuid.find(cacheIt->second);
        if (toolIt != m_toolParametersByUuid.end()) {
            toolInfo = toolIt->second;
            return ErrorCode::Success;
        }
    }

    // 如果缓存中没有找到，回退到宏变量查找（兼容性保证）
    int mappedValue = static_cast<int>(m_interface.m_macroPublicParamPtr[0].m_shmPtr->m_dPublicUserVar[20200 + hNumber - 500]);
    if (mappedValue <= 0) {
        m_interface.m_lastError = "H" + std::to_string(hNumber) + " 未映射到任何刀具";
        return ErrorCode::InvalidState;
    }

    // 解析刀号和刀沿
    int toolNumber = mappedValue / 100;
    int dNumber = mappedValue % 100;

    // 在刀具列表中查找对应的刀具
    auto range = m_toolNumberToUuidIndex.equal_range(toolNumber);
    for (auto it = range.first; it != range.second; ++it) {
        auto toolIt = m_toolParametersByUuid.find(it->second);
        if (toolIt != m_toolParametersByUuid.end() && 
            toolIt->second.isValid && 
            toolIt->second.dNumber == dNumber) {
            toolInfo = toolIt->second;
            return ErrorCode::Success;
        }
    }

    m_interface.m_lastError = "H" + std::to_string(hNumber) + " 映射的刀具T" + std::to_string(toolNumber) +
                            ".D" + std::to_string(dNumber) + " 在刀具列表中不存在";
    return ErrorCode::InvalidState;
}

ErrorCode ToolManagerSimple::getToolCompensationByHNumber(int channelId, int hNumber, ToolInfo& toolInfo) {
    if (channelId < 0 || channelId >= m_interface.m_loadedChannelsCount) {
        m_interface.m_lastError = "无效的通道ID: " + std::to_string(channelId);
        return ErrorCode::InvalidParam;
    }

    if (hNumber <= 0 || hNumber > 100) {
        m_interface.m_lastError = "无效的H号: " + std::to_string(hNumber);
        return ErrorCode::InvalidParam;
    }

    // 从ToolPara数组直接读取刀补数据
    int hIndex = hNumber - 1; // ToolPara数组索引从0开始
    const TOOL_PARA_PTR& toolPara = m_interface.m_toolsParamPtr[channelId].m_shmPtr->m_ToolPara[hIndex];

    // 先获取刀具基本信息
    ErrorCode result = getToolInfoByHNumber(hNumber, toolInfo);
    if (result != ErrorCode::Success) {
        // 如果找不到刀具信息，创建一个基本的刀具信息结构
        toolInfo = {};
        toolInfo.isValid = false;
        toolInfo.number = 0;
        toolInfo.dNumber = 0;
        toolInfo.name = "H" + std::to_string(hNumber) + " 未知刀具";
    }

    // 从ToolPara读取实时刀补数据
    toolInfo.geometryLengthX = toolPara.m_ToolLen[0];
    toolInfo.geometryLengthXWear = toolPara.m_ToolHComp[0];
    toolInfo.geometryLengthY = toolPara.m_ToolLen[1];
    toolInfo.geometryLengthYWear = toolPara.m_ToolHComp[1];
    toolInfo.geometryLengthZ = toolPara.m_ToolLen[2];
    toolInfo.geometryLengthZWear = toolPara.m_ToolHComp[2];
    toolInfo.geometryRadius = toolPara.m_ToolR;
    toolInfo.geometryRadiusWear = toolPara.m_ToolDComp;
    toolInfo.activeLengthOffset = toolPara.m_ToolHComp[2];
    toolInfo.activeRadiusOffset = toolPara.m_ToolDComp;

    return ErrorCode::Success;
}

ErrorCode ToolManagerSimple::syncHMappingToMacroVars() {
    std::lock_guard<std::mutex> lock(m_toolMutex);

    if (!m_isInitialized) {
        return ErrorCode::NotInitialized;
    }

    // 重新构建H映射表
    updateHMappingTable();

    return ErrorCode::Success;
}

void ToolManagerSimple::initializeSystemMagazine() {
    std::lock_guard<std::mutex> lock(m_toolMutex);

    // 刀库基本参数
    m_interface.m_macroPublicParamPtr[0].m_shmPtr->m_dPublicUserVar[20000 - 500] = 0;  // 刀库类型：0=无刀库
    m_interface.m_macroPublicParamPtr[0].m_shmPtr->m_dPublicUserVar[20001 - 500] = 24;  // 刀库总刀数
    m_interface.m_macroPublicParamPtr[0].m_shmPtr->m_dPublicUserVar[20003 - 500] = 8;  // 主轴当前刀号
    m_interface.m_macroPublicParamPtr[0].m_shmPtr->m_dPublicUserVar[20004 - 500] = 1;  // 刀库当前位置
    m_interface.m_macroPublicParamPtr[0].m_shmPtr->m_dPublicUserVar[20005 - 500] = 0;  // 刀库备刀刀号

    // 刀号对应刀套号映射（25个刀位）
    for (int i = 0; i < 25; i++) {
        m_interface.m_macroPublicParamPtr[0].m_shmPtr->m_dPublicUserVar[20031 + i - 500] = i + 1;
    }

    // 初始化H映射表
    initializeHMappingTable();
}

void ToolManagerSimple::initializeToolData() {
    // 注意：此方法假设已经获得了m_toolMutex锁

    // 清空现有数据
    m_toolParametersByUuid.clear();
    m_toolNumberToUuidIndex.clear();
    clearHMappingCache();

    // 创建默认刀具数据
    struct DefaultToolData {
        int number;
        int dNumber;
        std::string name;
        int toolTypeCode;
        double lengthZ;
        double radius;
        int toolChangerId;
        int pocket;
    };

    std::vector<DefaultToolData> defaultTools = {
        {1, 1, "10mm立铣刀", 100, 50.0, 5.0, 0, 1},
        {2, 1, "6mm立铣刀", 100, 45.0, 3.0, 0, 2},
        {3, 1, "8mm钻头", 200, 60.0, 4.0, 0, 3},
        {4, 1, "M8丝锥", 300, 40.0, 4.0, 0, 4},
        {5, 1, "20mm面铣刀", 140, 35.0, 10.0, 0, 5},
        {6, 1, "12mm球头铣刀", 110, 55.0, 6.0, 0, 6},
        {7, 1, "5mm钻头", 200, 50.0, 2.5, 0, 7},
        {8, 1, "15mm立铣刀", 100, 60.0, 7.5, 0, 8}
    };

    for (const auto& toolData : defaultTools) {
        ToolInfo toolInfo;
        toolInfo.uuid = generateUuid();
        toolInfo.number = toolData.number;
        toolInfo.dNumber = toolData.dNumber;
        toolInfo.name = toolData.name;
        toolInfo.toolTypeCode = toolData.toolTypeCode;
        toolInfo.toolDirection = 1; // 默认方向
        toolInfo.numberOfFlutes = (toolData.toolTypeCode == 100) ? 4 : 2; // 立铣刀4刃，其他2刃
        toolInfo.sisterToolNumber = 1;

        // 几何参数
        toolInfo.geometryLengthZ = toolData.lengthZ;
        toolInfo.geometryRadius = toolData.radius;
        toolInfo.geometryLengthX = 0.0;
        toolInfo.geometryLengthY = 0.0;

        // 刀库位置
        toolInfo.toolChangerId = toolData.toolChangerId;
        toolInfo.pocket = toolData.pocket;

        // 状态
        toolInfo.isValid = true;
        toolInfo.isEnabled = true;
        toolInfo.isActive = (toolData.number == 1); // 第一个刀具设为活动

        // 寿命数据（设置一些示例值）
        toolInfo.lifeData.nominalLifeSeconds = 3600.0; // 1小时
        toolInfo.lifeData.warningLifeSeconds = 3000.0; // 50分钟预警
        toolInfo.lifeData.nominalUsageCount = 100;
        toolInfo.lifeData.warningUsageCount = 80;

        m_toolParametersByUuid[toolInfo.uuid] = toolInfo;
        updateToolNumberIndex(toolInfo);
    }

    // 重建H映射表
    updateHMappingTable();

    qDebug() << "初始化了" << defaultTools.size() << "个默认刀具";
}

// === 刀库管理实现 ===

ErrorCode ToolManagerSimple::getToolInfoInPocket(int toolChangerId, int pocketNumber, ToolInfo& toolInfo) {
    std::lock_guard<std::mutex> lock(m_toolMutex);

    toolInfo = {};
    toolInfo.isValid = false;

    if (!m_isInitialized) {
        return ErrorCode::NotInitialized;
    }

    // 在刀具列表中查找位于指定刀库和刀位的刀具
    for (const auto& [uuid, tool] : m_toolParametersByUuid) {
        if (tool.toolChangerId == toolChangerId && tool.pocket == pocketNumber && tool.isValid) {
            toolInfo = tool;
            return ErrorCode::Success;
        }
    }

    return ErrorCode::InvalidState;
}

ErrorCode ToolManagerSimple::loadToolIntoPocket(int toolChangerId, int pocketNumber, int toolNumber) {
    std::lock_guard<std::mutex> lock(m_toolMutex);

    if (!m_isInitialized) {
        return ErrorCode::NotInitialized;
    }

    // 通过刀号索引查找对应的UUID
    std::string targetUuid;
    auto range = m_toolNumberToUuidIndex.equal_range(toolNumber);
    if (range.first == range.second) {
        m_interface.m_lastError = "装载刀具到刀位错误: 未找到刀具号 " + std::to_string(toolNumber) + " 对应的刀具";
        return ErrorCode::InvalidParam;
    }

    // 查找有效的刀具
    ToolInfo tempToolInfo;
    bool foundValidTool = false;
    for (auto it = range.first; it != range.second; ++it) {
        auto toolIt = m_toolParametersByUuid.find(it->second);
        if (toolIt != m_toolParametersByUuid.end() && toolIt->second.isValid) {
            tempToolInfo = toolIt->second;
            targetUuid = it->second;
            foundValidTool = true;
            break;
        }
    }

    if (!foundValidTool) {
        m_interface.m_lastError = "装载刀具到刀位错误: 刀具号 " + std::to_string(toolNumber) + " 无有效刀具";
        return ErrorCode::InvalidParam;
    }

    // 检查刀位是否已被其他刀具占用
    for (const auto& [uuid, tool] : m_toolParametersByUuid) {
        if (tool.toolChangerId == toolChangerId && tool.pocket == pocketNumber && tool.isValid && uuid != targetUuid) {
            m_interface.m_lastError = "装载刀具到刀位错误: 刀位 " + std::to_string(pocketNumber) + " 已被刀具 " +
                          std::to_string(tool.number) + " 占用。";
            return ErrorCode::InvalidState;
        }
    }

    // 更新刀具的位置信息
    m_toolParametersByUuid[targetUuid].toolChangerId = toolChangerId;
    m_toolParametersByUuid[targetUuid].pocket = pocketNumber;

    return ErrorCode::Success;
}

ErrorCode ToolManagerSimple::unloadToolFromPocket(int toolChangerId, int pocketNumber, int& unloadedToolNumber) {
    std::lock_guard<std::mutex> lock(m_toolMutex);

    unloadedToolNumber = 0;
    if (!m_isInitialized) {
        return ErrorCode::NotInitialized;
    }

    // 在刀具列表中查找位于指定刀库和刀位的刀具
    for (auto& [uuid, tool] : m_toolParametersByUuid) {
        if (tool.toolChangerId == toolChangerId && tool.pocket == pocketNumber && tool.isValid) {
            unloadedToolNumber = tool.number;
            // 将刀具标记为不在刀库中
            tool.toolChangerId = -1;
            tool.pocket = -1;
            return ErrorCode::Success;
        }
    }

    return ErrorCode::OperationFailed;
}

ErrorCode ToolManagerSimple::exchangeToolsInPockets(int toolChangerId, int pocketNumber1, int pocketNumber2) {
    std::lock_guard<std::mutex> lock(m_toolMutex);

    if (!m_isInitialized) {
        return ErrorCode::NotInitialized;
    }

    if (pocketNumber1 == pocketNumber2) {
        return ErrorCode::Success; // 相同刀位，无需交换
    }

    // 查找两个刀位中的刀具
    std::string uuidInPocket1;
    std::string uuidInPocket2;

    for (const auto& [uuid, tool] : m_toolParametersByUuid) {
        if (tool.toolChangerId == toolChangerId && tool.isValid) {
            if (tool.pocket == pocketNumber1) {
                uuidInPocket1 = uuid;
            } else if (tool.pocket == pocketNumber2) {
                uuidInPocket2 = uuid;
            }
        }
    }

    // 交换两个刀具的位置
    if (!uuidInPocket1.empty()) {
        m_toolParametersByUuid[uuidInPocket1].pocket = pocketNumber2;
    }
    if (!uuidInPocket2.empty()) {
        m_toolParametersByUuid[uuidInPocket2].pocket = pocketNumber1;
    }

    return ErrorCode::Success;
}

ErrorCode ToolManagerSimple::getToolsByMagazine(int toolChangerId, std::vector<ToolInfo>& toolsInMagazine) {
    std::lock_guard<std::mutex> lock(m_toolMutex);

    if (!m_isInitialized) {
        return ErrorCode::NotInitialized;
    }

    toolsInMagazine.clear();

    // 遍历所有刀具，筛选指定刀库中的刀具
    for (const auto& [uuid, tool] : m_toolParametersByUuid) {
        if (tool.isValid && tool.toolChangerId == toolChangerId) {
            toolsInMagazine.push_back(tool);
        }
    }

    return ErrorCode::Success;
}

// === 内部辅助方法实现 ===

std::string ToolManagerSimple::generateUuid() {
    // 简化的UUID生成（实际项目中应使用更好的UUID库）
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<> dis(0, 15);

    std::stringstream ss;
    ss << std::hex;
    for (int i = 0; i < 32; ++i) {
        if (i == 8 || i == 12 || i == 16 || i == 20) {
            ss << "-";
        }
        ss << dis(gen);
    }
    return ss.str();
}

void ToolManagerSimple::updateToolNumberIndex(const ToolInfo& toolInfo) {
    // 注意：此方法假设已经获得了m_toolMutex锁

    // 先移除旧的索引（如果存在）
    removeFromToolNumberIndex(toolInfo.uuid);

    // 添加新的索引
    if (toolInfo.isValid && toolInfo.number > 0) {
        m_toolNumberToUuidIndex.emplace(toolInfo.number, toolInfo.uuid);
    }
}

void ToolManagerSimple::removeFromToolNumberIndex(const std::string& uuid) {
    // 注意：此方法假设已经获得了m_toolMutex锁

    // 查找并移除所有匹配的条目
    for (auto it = m_toolNumberToUuidIndex.begin(); it != m_toolNumberToUuidIndex.end();) {
        if (it->second == uuid) {
            it = m_toolNumberToUuidIndex.erase(it);
        } else {
            ++it;
        }
    }
}

void ToolManagerSimple::clearHMappingCache() {
    // 注意：此方法假设已经获得了m_toolMutex锁
    m_hNumberToUuidCache.clear();
    m_uuidToHNumberCache.clear();
    m_hNumberToToolNumberAndDCache.clear();
}

ErrorCode ToolManagerSimple::setToolToDeviceByHIndex(int channelId, int hIndex, const ToolInfo& toolInfo) {
    // 刀具信息保存到SDK（按H号索引）
    // hIndex对应ToolPara数组的索引，与H号一一对应
    TOOL_PARA_PTR& toolPara = m_interface.m_toolsParamPtr[channelId].m_shmPtr->m_ToolPara[hIndex];

    // 刀具X轴
    toolPara.m_ToolLen[0] = toolInfo.geometryLengthX;
    toolPara.m_ToolHComp[0] = toolInfo.geometryLengthXWear;

    // 刀具Y轴
    toolPara.m_ToolLen[1] = toolInfo.geometryLengthY;
    toolPara.m_ToolHComp[1] = toolInfo.geometryLengthYWear;

    // 刀具Z轴
    toolPara.m_ToolLen[2] = toolInfo.geometryLengthZ;
    toolPara.m_ToolHComp[2] = toolInfo.geometryLengthZWear;

    // 刀具半径
    toolPara.m_ToolR = toolInfo.geometryRadius;
    toolPara.m_ToolDComp = toolInfo.geometryRadiusWear;

    // TOFF：计算当前激活的补偿值？？
    // toolPara.m_ToolHComp[2] = toolInfo.activeLengthOffset;
    // toolPara.m_ToolDComp = toolInfo.activeRadiusOffset;

    return ErrorCode::Success;
}

// === 配置文件管理实现 ===

ErrorCode ToolManagerSimple::loadToolDataFromConfig() {
    std::string configPath = getToolDataConfigPath();

    QFile file(QString::fromStdString(configPath));
    if (!file.exists()) {
        qDebug() << "刀具配置文件不存在:" << QString::fromStdString(configPath);
        return ErrorCode::FileNotFound;
    }

    if (!file.open(QIODevice::ReadOnly)) {
        m_interface.m_lastError = "无法打开刀具配置文件: " + configPath;
        return ErrorCode::FileError;
    }

    QByteArray data = file.readAll();
    file.close();

    QJsonParseError parseError;
    QJsonDocument doc = QJsonDocument::fromJson(data, &parseError);

    if (parseError.error != QJsonParseError::NoError) {
        m_interface.m_lastError = "解析刀具配置文件失败: " + parseError.errorString().toStdString();
        return ErrorCode::FileError;
    }

    QJsonObject rootObj = doc.object();

    // 检查版本兼容性
    QString version = rootObj["version"].toString();
    if (version.isEmpty()) {
        qDebug() << "刀具配置文件缺少版本信息，尝试加载";
    }

    // 清空现有数据
    m_toolParametersByUuid.clear();
    m_toolNumberToUuidIndex.clear();
    clearHMappingCache();

    // 加载刀具参数
    QJsonArray toolArray = rootObj["tool_parameters"].toArray();
    int loadedCount = 0;

    for (const QJsonValue& value : toolArray) {
        if (!value.isObject()) continue;

        QJsonObject toolObj = value.toObject();
        ToolInfo toolInfo = createToolInfoFromJson(toolObj);

        if (validateToolInfo(toolInfo)) {
            m_toolParametersByUuid[toolInfo.uuid] = toolInfo;
            updateToolNumberIndex(toolInfo);
            loadedCount++;
        } else {
            qDebug() << "跳过无效的刀具数据，UUID:" << QString::fromStdString(toolInfo.uuid);
        }
    }

    qDebug() << "成功加载" << loadedCount << "个刀具";
    return ErrorCode::Success;
}

ErrorCode ToolManagerSimple::saveToolDataToConfig() {
    std::lock_guard<std::mutex> lock(m_toolMutex);

    if (!m_isInitialized) {
        return ErrorCode::NotInitialized;
    }

    std::string configPath = getToolDataConfigPath();

    // 确保目录存在
    QFileInfo fileInfo(QString::fromStdString(configPath));
    QDir dir = fileInfo.absoluteDir();
    if (!dir.exists()) {
        if (!dir.mkpath(".")) {
            m_interface.m_lastError = "无法创建刀具配置目录: " + dir.absolutePath().toStdString();
            return ErrorCode::FileError;
        }
    }

    // 构建JSON文档
    QJsonObject rootObj;
    rootObj["timestamp"] = QDateTime::currentDateTime().toString(Qt::ISODate);
    rootObj["version"] = "1.2";

    QJsonArray toolArray;
    for (const auto& [uuid, toolInfo] : m_toolParametersByUuid) {
        if (toolInfo.isValid) {
            QJsonObject toolObj = createJsonFromToolInfo(toolInfo);
            toolArray.append(toolObj);
        }
    }

    rootObj["tool_parameters"] = toolArray;

    QJsonDocument doc(rootObj);

    // 写入文件
    QFile file(QString::fromStdString(configPath));
    if (!file.open(QIODevice::WriteOnly)) {
        m_interface.m_lastError = "无法写入刀具配置文件: " + configPath;
        return ErrorCode::FileError;
    }

    QByteArray data = doc.toJson(QJsonDocument::Indented);
    qint64 bytesWritten = file.write(data);
    file.close();

    if (bytesWritten != data.size()) {
        m_interface.m_lastError = "刀具配置文件写入不完整";
        return ErrorCode::FileError;
    }

    qDebug() << "成功保存" << m_toolParametersByUuid.size() << "个刀具到配置文件";
    return ErrorCode::Success;
}

std::string ToolManagerSimple::getToolDataConfigPath() {
    // 获取应用程序数据目录
    QString appDataPath = QStandardPaths::writableLocation(QStandardPaths::AppDataLocation);
    if (appDataPath.isEmpty()) {
        appDataPath = QDir::currentPath() + "/configs";
    }

    QDir configDir(appDataPath + "/TOOLS");
    if (!configDir.exists()) {
        configDir.mkpath(".");
    }

    QString configPath = configDir.absoluteFilePath("ToolData.json");
    return configPath.toStdString();
}

ToolInfo ToolManagerSimple::createToolInfoFromJson(const QJsonObject& jsonObj) {
    ToolInfo toolInfo;

    // 基本信息
    toolInfo.uuid = jsonObj["uuid"].toString().toStdString();
    toolInfo.name = jsonObj["name"].toString().toStdString();
    toolInfo.number = jsonObj["number"].toInt();
    toolInfo.toolChangerId = jsonObj["toolChangerId"].toInt(-1);
    toolInfo.pocket = jsonObj["pocket"].toInt(0);

    // 刀具描述与分类
    toolInfo.toolTypeCode = jsonObj["toolTypeCode"].toInt(0);
    toolInfo.toolDirection = jsonObj["toolDirection"].toInt(1);
    toolInfo.numberOfFlutes = jsonObj["numberOfFlutes"].toInt(0);
    toolInfo.sisterToolNumber = jsonObj["sisterToolNumber"].toInt(1);
    toolInfo.dNumber = jsonObj["dNumber"].toInt(1);

    // 几何参数
    toolInfo.geometryLengthZ = jsonObj["geometryLengthZ"].toDouble(0.0);
    toolInfo.geometryLengthZWear = jsonObj["geometryLengthZWear"].toDouble(0.0);
    toolInfo.geometryRadius = jsonObj["geometryRadius"].toDouble(0.0);
    toolInfo.geometryRadiusWear = jsonObj["geometryRadiusWear"].toDouble(0.0);
    toolInfo.geometryLengthX = jsonObj["geometryLengthX"].toDouble(0.0);
    toolInfo.geometryLengthXWear = jsonObj["geometryLengthXWear"].toDouble(0.0);
    toolInfo.geometryLengthY = jsonObj["geometryLengthY"].toDouble(0.0);
    toolInfo.geometryLengthYWear = jsonObj["geometryLengthYWear"].toDouble(0.0);
    toolInfo.toolWidth = jsonObj["toolWidth"].toDouble(0.0);
    toolInfo.cuttingEdgeAngle = jsonObj["cuttingEdgeAngle"].toDouble(0.0);
    toolInfo.tipAngle = jsonObj["tipAngle"].toDouble(0.0);
    toolInfo.noseRadius = jsonObj["noseRadius"].toDouble(0.0);

    // 当前激活的补偿值
    toolInfo.activeLengthOffset = jsonObj["activeLengthOffset"].toDouble(0.0);
    toolInfo.activeRadiusOffset = jsonObj["activeRadiusOffset"].toDouble(0.0);

    // 状态标志
    toolInfo.isActive = jsonObj["isActive"].toBool(false);
    toolInfo.isEnabled = jsonObj["isEnabled"].toBool(true);
    toolInfo.isMeasured = jsonObj["isMeasured"].toBool(false);
    toolInfo.isLifeWarningReached = jsonObj["isLifeWarningReached"].toBool(false);
    toolInfo.isChanging = jsonObj["isChanging"].toBool(false);
    toolInfo.isInFixedLocation = jsonObj["isInFixedLocation"].toBool(false);
    toolInfo.wasUsed = jsonObj["wasUsed"].toBool(false);
    toolInfo.isValid = jsonObj["isValid"].toBool(true);

    // 寿命数据
    if (jsonObj.contains("lifeData")) {
        QJsonObject lifeDataObj = jsonObj["lifeData"].toObject();
        toolInfo.lifeData.nominalLifeSeconds = lifeDataObj["nominalLifeSeconds"].toDouble(0.0);
        toolInfo.lifeData.usedLifeSeconds = lifeDataObj["usedLifeSeconds"].toDouble(0.0);
        toolInfo.lifeData.warningLifeSeconds = lifeDataObj["warningLifeSeconds"].toDouble(0.0);
        toolInfo.lifeData.nominalUsageCount = lifeDataObj["nominalUsageCount"].toInt(0);
        toolInfo.lifeData.currentUsageCount = lifeDataObj["currentUsageCount"].toInt(0);
        toolInfo.lifeData.warningUsageCount = lifeDataObj["warningUsageCount"].toInt(0);
        toolInfo.lifeData.currentWearValue = lifeDataObj["currentWearValue"].toDouble(0.0);
        toolInfo.lifeData.maxWearValue = lifeDataObj["maxWearValue"].toDouble(0.0);
        toolInfo.lifeData.warningWearValue = lifeDataObj["warningWearValue"].toDouble(0.0);
    }

    // 主轴控制与冷却液设置
    toolInfo.spindleDirection = static_cast<SpindleDirection>(jsonObj["spindleDirection"].toInt(0));
    toolInfo.coolant1Enabled = jsonObj["coolant1Enabled"].toBool(false);
    toolInfo.coolant2Enabled = jsonObj["coolant2Enabled"].toBool(false);

    return toolInfo;
}

QJsonObject ToolManagerSimple::createJsonFromToolInfo(const ToolInfo& toolInfo) {
    QJsonObject jsonObj;

    // 基本信息
    jsonObj["uuid"] = QString::fromStdString(toolInfo.uuid);
    jsonObj["name"] = QString::fromStdString(toolInfo.name);
    jsonObj["number"] = toolInfo.number;
    jsonObj["toolChangerId"] = toolInfo.toolChangerId;
    jsonObj["pocket"] = toolInfo.pocket;

    // 刀具描述与分类
    jsonObj["toolTypeCode"] = toolInfo.toolTypeCode;
    jsonObj["toolDirection"] = toolInfo.toolDirection;
    jsonObj["numberOfFlutes"] = toolInfo.numberOfFlutes;
    jsonObj["sisterToolNumber"] = toolInfo.sisterToolNumber;
    jsonObj["dNumber"] = toolInfo.dNumber;

    // 几何参数
    jsonObj["geometryLengthZ"] = toolInfo.geometryLengthZ;
    jsonObj["geometryLengthZWear"] = toolInfo.geometryLengthZWear;
    jsonObj["geometryRadius"] = toolInfo.geometryRadius;
    jsonObj["geometryRadiusWear"] = toolInfo.geometryRadiusWear;
    jsonObj["geometryLengthX"] = toolInfo.geometryLengthX;
    jsonObj["geometryLengthXWear"] = toolInfo.geometryLengthXWear;
    jsonObj["geometryLengthY"] = toolInfo.geometryLengthY;
    jsonObj["geometryLengthYWear"] = toolInfo.geometryLengthYWear;
    jsonObj["toolWidth"] = toolInfo.toolWidth;
    jsonObj["cuttingEdgeAngle"] = toolInfo.cuttingEdgeAngle;
    jsonObj["tipAngle"] = toolInfo.tipAngle;
    jsonObj["noseRadius"] = toolInfo.noseRadius;

    // 当前激活的补偿值
    jsonObj["activeLengthOffset"] = toolInfo.activeLengthOffset;
    jsonObj["activeRadiusOffset"] = toolInfo.activeRadiusOffset;

    // 状态标志
    jsonObj["isActive"] = toolInfo.isActive;
    jsonObj["isEnabled"] = toolInfo.isEnabled;
    jsonObj["isMeasured"] = toolInfo.isMeasured;
    jsonObj["isLifeWarningReached"] = toolInfo.isLifeWarningReached;
    jsonObj["isChanging"] = toolInfo.isChanging;
    jsonObj["isInFixedLocation"] = toolInfo.isInFixedLocation;
    jsonObj["wasUsed"] = toolInfo.wasUsed;
    jsonObj["isValid"] = toolInfo.isValid;

    // 寿命数据
    QJsonObject lifeDataObj;
    lifeDataObj["nominalLifeSeconds"] = toolInfo.lifeData.nominalLifeSeconds;
    lifeDataObj["usedLifeSeconds"] = toolInfo.lifeData.usedLifeSeconds;
    lifeDataObj["warningLifeSeconds"] = toolInfo.lifeData.warningLifeSeconds;
    lifeDataObj["nominalUsageCount"] = toolInfo.lifeData.nominalUsageCount;
    lifeDataObj["currentUsageCount"] = toolInfo.lifeData.currentUsageCount;
    lifeDataObj["warningUsageCount"] = toolInfo.lifeData.warningUsageCount;
    lifeDataObj["currentWearValue"] = toolInfo.lifeData.currentWearValue;
    lifeDataObj["maxWearValue"] = toolInfo.lifeData.maxWearValue;
    lifeDataObj["warningWearValue"] = toolInfo.lifeData.warningWearValue;
    jsonObj["lifeData"] = lifeDataObj;

    // 主轴控制与冷却液设置
    jsonObj["spindleDirection"] = static_cast<int>(toolInfo.spindleDirection);
    jsonObj["coolant1Enabled"] = toolInfo.coolant1Enabled;
    jsonObj["coolant2Enabled"] = toolInfo.coolant2Enabled;

    return jsonObj;
}

bool ToolManagerSimple::validateToolInfo(const ToolInfo& toolInfo) {
    // 检查必要字段
    if (toolInfo.uuid.empty()) {
        qDebug() << "刀具验证失败：UUID为空";
        return false;
    }

    if (toolInfo.name.empty()) {
        qDebug() << "刀具验证失败：名称为空";
        return false;
    }

    if (toolInfo.number <= 0) {
        qDebug() << "刀具验证失败：刀号无效" << toolInfo.number;
        return false;
    }

    if (toolInfo.dNumber <= 0 || toolInfo.dNumber > 9) {
        qDebug() << "刀具验证失败：刀沿号无效" << toolInfo.dNumber;
        return false;
    }

    // 检查几何参数的合理性
    if (toolInfo.geometryRadius < 0 || toolInfo.geometryLengthZ < 0) {
        qDebug() << "刀具验证失败：几何参数不能为负数";
        return false;
    }

    // 检查刀具类型代码
    if (toolInfo.toolTypeCode < 0) {
        qDebug() << "刀具验证失败：刀具类型代码无效" << toolInfo.toolTypeCode;
        return false;
    }

    return true;
}

void ToolManagerSimple::updateToolLifeData(const std::string& uuid, double usageSeconds, int usageCount) {
    std::lock_guard<std::mutex> lock(m_toolMutex);

    auto it = m_toolParametersByUuid.find(uuid);
    if (it == m_toolParametersByUuid.end()) {
        return;
    }

    ToolInfo& toolInfo = it->second;

    // 更新使用时间
    if (usageSeconds > 0) {
        toolInfo.lifeData.usedLifeSeconds += usageSeconds;
        toolInfo.wasUsed = true;
    }

    // 更新使用次数
    if (usageCount > 0) {
        toolInfo.lifeData.currentUsageCount += usageCount;
        toolInfo.wasUsed = true;
    }

    // 检查是否达到预警阈值
    bool warningReached = false;

    if (toolInfo.lifeData.warningLifeSeconds > 0 &&
        toolInfo.lifeData.usedLifeSeconds >= toolInfo.lifeData.warningLifeSeconds) {
        warningReached = true;
    }

    if (toolInfo.lifeData.warningUsageCount > 0 &&
        toolInfo.lifeData.currentUsageCount >= toolInfo.lifeData.warningUsageCount) {
        warningReached = true;
    }

    if (toolInfo.lifeData.warningWearValue > 0 &&
        toolInfo.lifeData.currentWearValue >= toolInfo.lifeData.warningWearValue) {
        warningReached = true;
    }

    if (warningReached && !toolInfo.isLifeWarningReached) {
        toolInfo.isLifeWarningReached = true;
        qDebug() << "刀具" << QString::fromStdString(toolInfo.name) << "达到寿命预警阈值";

        // 这里可以触发报警或通知
        // m_interface.setAlarm(...);
    }
}
