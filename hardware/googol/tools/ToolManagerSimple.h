#pragma once

#include <map>
#include <mutex>
#include <string>
#include <vector>
#include "ICncInterface.h"

// 前向声明
class GoogolCncInterface;

/**
 * @brief 简化的刀具管理器
 * 
 * 负责管理所有刀具相关功能，包括：
 * 1. 刀具数据管理（增删改查）
 * 2. H映射表管理
 * 3. 刀具参数同步到SDK
 * 4. 缓存管理优化
 */
class ToolManagerSimple {
public:
    /**
     * @brief 构造函数
     * @param interface GoogolCncInterface的引用
     */
    explicit ToolManagerSimple(GoogolCncInterface& interface);
    
    /**
     * @brief 析构函数
     */
    ~ToolManagerSimple() = default;

    // 禁用拷贝构造和赋值
    ToolManagerSimple(const ToolManagerSimple&) = delete;
    ToolManagerSimple& operator=(const ToolManagerSimple&) = delete;

    /**
     * @brief 初始化刀具管理器
     * @return ErrorCode 操作结果
     */
    ErrorCode initialize();

    // === 刀具数据管理接口 ===
    
    /**
     * @brief 获取当前主轴刀具信息
     * @param channelId 通道ID
     * @param toolInfo 输出的刀具信息
     * @return ErrorCode 操作结果
     */
    ErrorCode getCurrentToolInfo(int channelId, ToolInfo& toolInfo);

    /**
     * @brief 根据UUID获取刀具参数
     * @param uuid 刀具UUID
     * @param toolInfo 输出的刀具信息
     * @return ErrorCode 操作结果
     */
    ErrorCode getToolParameters(const std::string& uuid, ToolInfo& toolInfo);

    /**
     * @brief 设置刀具参数
     * @param toolInfo 刀具信息（如果UUID为空则创建新刀具）
     * @return ErrorCode 操作结果
     */
    ErrorCode setToolParameters(ToolInfo& toolInfo);

    /**
     * @brief 删除刀具
     * @param uuid 刀具UUID
     * @return ErrorCode 操作结果
     */
    ErrorCode deleteTool(const std::string& uuid);

    /**
     * @brief 获取所有刀具参数
     * @param allToolsInfo 输出的所有刀具信息
     * @return ErrorCode 操作结果
     */
    ErrorCode getAllToolParameters(std::map<std::string, ToolInfo>& allToolsInfo);

    // === 刀库管理接口 ===

    /**
     * @brief 获取刀位中的刀具信息
     * @param toolChangerId 刀库ID
     * @param pocketNumber 刀位号
     * @param toolInfo 输出的刀具信息
     * @return ErrorCode 操作结果
     */
    ErrorCode getToolInfoInPocket(int toolChangerId, int pocketNumber, ToolInfo& toolInfo);

    /**
     * @brief 将刀具装载到刀位
     * @param toolChangerId 刀库ID
     * @param pocketNumber 刀位号
     * @param toolNumber 刀号
     * @return ErrorCode 操作结果
     */
    ErrorCode loadToolIntoPocket(int toolChangerId, int pocketNumber, int toolNumber);

    /**
     * @brief 从刀位卸载刀具
     * @param toolChangerId 刀库ID
     * @param pocketNumber 刀位号
     * @param unloadedToolNumber 输出卸载的刀号
     * @return ErrorCode 操作结果
     */
    ErrorCode unloadToolFromPocket(int toolChangerId, int pocketNumber, int& unloadedToolNumber);

    /**
     * @brief 交换两个刀位中的刀具
     * @param toolChangerId 刀库ID
     * @param pocketNumber1 刀位号1
     * @param pocketNumber2 刀位号2
     * @return ErrorCode 操作结果
     */
    ErrorCode exchangeToolsInPockets(int toolChangerId, int pocketNumber1, int pocketNumber2);

    /**
     * @brief 获取刀库中的所有刀具
     * @param toolChangerId 刀库ID
     * @param toolsInMagazine 输出的刀具列表
     * @return ErrorCode 操作结果
     */
    ErrorCode getToolsByMagazine(int toolChangerId, std::vector<ToolInfo>& toolsInMagazine);

    // === H映射表管理接口 ===
    
    /**
     * @brief 初始化H映射表
     */
    void initializeHMappingTable();

    /**
     * @brief 更新H映射表
     */
    void updateHMappingTable();

    /**
     * @brief 查找刀具对应的H号
     * @param toolNumber 刀号
     * @param dNumber 刀沿号
     * @return int H号（0表示未找到）
     */
    int findHNumberForTool(int toolNumber, int dNumber);

    /**
     * @brief 根据H号获取刀具信息
     * @param hNumber H号
     * @param toolInfo 输出的刀具信息
     * @return ErrorCode 操作结果
     */
    ErrorCode getToolInfoByHNumber(int hNumber, ToolInfo& toolInfo);

    /**
     * @brief 根据H号获取刀补数据
     * @param channelId 通道ID
     * @param hNumber H号
     * @param toolInfo 输出的刀具信息（包含刀补数据）
     * @return ErrorCode 操作结果
     */
    ErrorCode getToolCompensationByHNumber(int channelId, int hNumber, ToolInfo& toolInfo);

    /**
     * @brief 同步H映射表到宏变量
     * @return ErrorCode 操作结果
     */
    ErrorCode syncHMappingToMacroVars();

    // === 系统初始化接口 ===
    
    /**
     * @brief 初始化系统刀库
     */
    void initializeSystemMagazine();

    /**
     * @brief 初始化刀具数据
     */
    void initializeToolData();

    // === 内部数据访问接口（供GoogolCncInterface使用） ===
    
    /**
     * @brief 获取刀具参数存储的引用
     * @return std::map<std::string, ToolInfo>& 刀具参数存储的引用
     */
    std::map<std::string, ToolInfo>& getToolParametersByUuid() { return m_toolParametersByUuid; }

    /**
     * @brief 获取刀号索引的引用
     * @return std::multimap<int, std::string>& 刀号索引的引用
     */
    std::multimap<int, std::string>& getToolNumberToUuidIndex() { return m_toolNumberToUuidIndex; }

    /**
     * @brief 获取工具互斥锁的引用
     * @return std::mutex& 互斥锁的引用
     */
    std::mutex& getToolMutex() { return m_toolMutex; }

private:
    GoogolCncInterface& m_interface;        // CNC接口引用
    mutable std::mutex m_toolMutex;         // 刀具数据访问锁
    bool m_isInitialized;                   // 初始化状态

    // 刀具参数存储
    std::map<std::string, ToolInfo> m_toolParametersByUuid;  // 主要存储：按UUID索引的刀具参数
    std::multimap<int, std::string> m_toolNumberToUuidIndex; // 辅助索引：刀号到UUID的映射

    // H号映射缓存（用空间换时间的优化）
    std::map<int, std::string> m_hNumberToUuidCache;         // H号到刀具UUID的直接映射
    std::map<std::string, int> m_uuidToHNumberCache;         // 刀具UUID到H号的反向映射
    std::map<int, int> m_hNumberToToolNumberAndDCache;       // H号到刀号+刀沿组合值的缓存

    // === 内部辅助方法 ===
    
    /**
     * @brief 生成新的UUID
     * @return std::string 新的UUID
     */
    std::string generateUuid();

    /**
     * @brief 更新刀号索引
     * @param toolInfo 刀具信息
     */
    void updateToolNumberIndex(const ToolInfo& toolInfo);

    /**
     * @brief 从刀号索引中移除
     * @param uuid 刀具UUID
     */
    void removeFromToolNumberIndex(const std::string& uuid);

    /**
     * @brief 清空H号映射缓存
     */
    void clearHMappingCache();

    /**
     * @brief 按H号索引设置刀补数据到ToolPara数组
     * @param channelId 通道ID
     * @param hIndex H号索引（从0开始）
     * @param toolInfo 刀具信息
     * @return ErrorCode 操作结果
     */
    ErrorCode setToolToDeviceByHIndex(int channelId, int hIndex, const ToolInfo& toolInfo);
};
