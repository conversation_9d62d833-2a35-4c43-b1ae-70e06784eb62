#include "GoogolToolManager.h"
#include "../GoogolCncInterface.h"
#include <iostream>
#include <cassert>

// 简单的测试函数
void testGoogolToolManagerMethods() {
    std::cout << "开始测试 GoogolToolManager 的刀库管理方法..." << std::endl;
    
    // 注意：这里需要一个实际的 GoogolCncInterface 实例
    // 以下代码仅为示例，实际使用时需要正确初始化接口
    
    /*
    GoogolCncInterface interface;
    GoogolToolManager toolManager(interface);
    
    // 测试初始化
    ErrorCode result = toolManager.initialize();
    assert(result == ErrorCode::Success);
    std::cout << "✓ 初始化成功" << std::endl;
    
    // 测试获取刀库状态
    std::vector<PocketStatus> pocketStatuses;
    result = toolManager.getMagazineStatus(0, pocketStatuses);
    if (result == ErrorCode::Success) {
        std::cout << "✓ 获取刀库状态成功，刀位数量: " << pocketStatuses.size() << std::endl;
        
        // 显示前几个刀位的状态
        for (size_t i = 0; i < std::min(size_t(5), pocketStatuses.size()); ++i) {
            const auto& status = pocketStatuses[i];
            std::cout << "  刀位 " << status.pocketNumber 
                      << ": " << (status.isOccupied ? "有刀具" : "空")
                      << ", 刀号: " << status.toolNumberInPocket
                      << ", 启用: " << (status.isEnabled ? "是" : "否")
                      << ", 工作位: " << (status.isWorkingPosition ? "是" : "否")
                      << std::endl;
        }
    } else {
        std::cout << "✗ 获取刀库状态失败" << std::endl;
    }
    
    // 测试创建刀具并装载到刀位
    ToolInfo newTool;
    newTool.name = "测试刀具";
    newTool.number = 10;
    newTool.dNumber = 1;
    newTool.geometryLengthZ = 60.0;
    newTool.geometryRadius = 5.0;
    newTool.isValid = true;
    
    result = toolManager.setToolParameters(newTool);
    if (result == ErrorCode::Success) {
        std::cout << "✓ 创建刀具成功，UUID: " << newTool.uuid << std::endl;
        
        // 装载到刀位
        result = toolManager.loadToolIntoPocket(0, 1, newTool.number);
        if (result == ErrorCode::Success) {
            std::cout << "✓ 刀具装载到刀位成功" << std::endl;
            
            // 测试从刀位移动到主轴
            result = toolManager.moveToolFromPocketToSpindle(0, 0, 1, 0);
            if (result == ErrorCode::Success) {
                std::cout << "✓ 刀具从刀位移动到主轴成功" << std::endl;
                
                // 测试获取当前主轴刀具
                ToolInfo currentTool;
                result = toolManager.getCurrentToolInfo(0, currentTool);
                if (result == ErrorCode::Success) {
                    std::cout << "✓ 获取当前主轴刀具成功: " << currentTool.name << std::endl;
                }
                
                // 测试从主轴移动回刀位
                int movedToolNumber = 0;
                result = toolManager.moveToolFromSpindleToPocket(0, 0, 0, 2, &movedToolNumber);
                if (result == ErrorCode::Success) {
                    std::cout << "✓ 刀具从主轴移动到刀位成功，刀号: " << movedToolNumber << std::endl;
                }
            }
        }
    }
    */
    
    std::cout << "GoogolToolManager 刀库管理方法测试完成！" << std::endl;
}

int main() {
    try {
        testGoogolToolManagerMethods();
        std::cout << "所有测试通过！" << std::endl;
        return 0;
    } catch (const std::exception& e) {
        std::cerr << "测试失败: " << e.what() << std::endl;
        return 1;
    }
}
